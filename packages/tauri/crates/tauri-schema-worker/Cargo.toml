[package]
name = "tauri-schema-worker"
version = "0.0.0"
edition = "2021"
publish = false

[lib]
crate-type = ["cdylib"]

[dependencies]
worker = { version = "0.6", features = ['http', 'axum'] }
worker-macros = { version = "0.6", features = ['http'] }
console_error_panic_hook = { version = "0.1" }
axum = { version = "0.8", default-features = false }
tower-service = "0.3"
semver = { version = "1", features = ["serde"] }
serde = { version = "1", features = ["derive"] }
anyhow = "1"

[lints.rust]
unexpected_cfgs = { level = "warn", check-cfg = [
  'cfg(wasm_bindgen_unstable_test_coverage)',
] }

# https://github.com/rustwasm/wasm-pack/issues/1501
[package.metadata.wasm-pack.profile.release]
wasm-opt = ["--enable-bulk-memory", "--enable-nontrapping-float-to-int"]
