## Default Permission

Default permissions for the plugin.

#### This default permission set includes the following:

- `allow-get-all-windows`
- `allow-scale-factor`
- `allow-inner-position`
- `allow-outer-position`
- `allow-inner-size`
- `allow-outer-size`
- `allow-is-fullscreen`
- `allow-is-minimized`
- `allow-is-maximized`
- `allow-is-focused`
- `allow-is-decorated`
- `allow-is-resizable`
- `allow-is-maximizable`
- `allow-is-minimizable`
- `allow-is-closable`
- `allow-is-visible`
- `allow-is-enabled`
- `allow-title`
- `allow-current-monitor`
- `allow-primary-monitor`
- `allow-monitor-from-point`
- `allow-available-monitors`
- `allow-cursor-position`
- `allow-theme`
- `allow-is-always-on-top`
- `allow-internal-toggle-maximize`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`core:window:allow-available-monitors`

</td>
<td>

Enables the available_monitors command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-available-monitors`

</td>
<td>

Denies the available_monitors command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-center`

</td>
<td>

Enables the center command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-center`

</td>
<td>

Denies the center command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-close`

</td>
<td>

Enables the close command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-close`

</td>
<td>

Denies the close command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-create`

</td>
<td>

Enables the create command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-create`

</td>
<td>

Denies the create command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-current-monitor`

</td>
<td>

Enables the current_monitor command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-current-monitor`

</td>
<td>

Denies the current_monitor command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-cursor-position`

</td>
<td>

Enables the cursor_position command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-cursor-position`

</td>
<td>

Denies the cursor_position command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-destroy`

</td>
<td>

Enables the destroy command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-destroy`

</td>
<td>

Denies the destroy command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-get-all-windows`

</td>
<td>

Enables the get_all_windows command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-get-all-windows`

</td>
<td>

Denies the get_all_windows command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-hide`

</td>
<td>

Enables the hide command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-hide`

</td>
<td>

Denies the hide command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-inner-position`

</td>
<td>

Enables the inner_position command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-inner-position`

</td>
<td>

Denies the inner_position command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-inner-size`

</td>
<td>

Enables the inner_size command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-inner-size`

</td>
<td>

Denies the inner_size command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-internal-toggle-maximize`

</td>
<td>

Enables the internal_toggle_maximize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-internal-toggle-maximize`

</td>
<td>

Denies the internal_toggle_maximize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-is-always-on-top`

</td>
<td>

Enables the is_always_on_top command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-is-always-on-top`

</td>
<td>

Denies the is_always_on_top command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-is-closable`

</td>
<td>

Enables the is_closable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-is-closable`

</td>
<td>

Denies the is_closable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-is-decorated`

</td>
<td>

Enables the is_decorated command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-is-decorated`

</td>
<td>

Denies the is_decorated command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-is-enabled`

</td>
<td>

Enables the is_enabled command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-is-enabled`

</td>
<td>

Denies the is_enabled command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-is-focused`

</td>
<td>

Enables the is_focused command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-is-focused`

</td>
<td>

Denies the is_focused command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-is-fullscreen`

</td>
<td>

Enables the is_fullscreen command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-is-fullscreen`

</td>
<td>

Denies the is_fullscreen command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-is-maximizable`

</td>
<td>

Enables the is_maximizable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-is-maximizable`

</td>
<td>

Denies the is_maximizable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-is-maximized`

</td>
<td>

Enables the is_maximized command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-is-maximized`

</td>
<td>

Denies the is_maximized command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-is-minimizable`

</td>
<td>

Enables the is_minimizable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-is-minimizable`

</td>
<td>

Denies the is_minimizable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-is-minimized`

</td>
<td>

Enables the is_minimized command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-is-minimized`

</td>
<td>

Denies the is_minimized command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-is-resizable`

</td>
<td>

Enables the is_resizable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-is-resizable`

</td>
<td>

Denies the is_resizable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-is-visible`

</td>
<td>

Enables the is_visible command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-is-visible`

</td>
<td>

Denies the is_visible command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-maximize`

</td>
<td>

Enables the maximize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-maximize`

</td>
<td>

Denies the maximize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-minimize`

</td>
<td>

Enables the minimize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-minimize`

</td>
<td>

Denies the minimize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-monitor-from-point`

</td>
<td>

Enables the monitor_from_point command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-monitor-from-point`

</td>
<td>

Denies the monitor_from_point command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-outer-position`

</td>
<td>

Enables the outer_position command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-outer-position`

</td>
<td>

Denies the outer_position command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-outer-size`

</td>
<td>

Enables the outer_size command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-outer-size`

</td>
<td>

Denies the outer_size command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-primary-monitor`

</td>
<td>

Enables the primary_monitor command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-primary-monitor`

</td>
<td>

Denies the primary_monitor command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-request-user-attention`

</td>
<td>

Enables the request_user_attention command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-request-user-attention`

</td>
<td>

Denies the request_user_attention command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-scale-factor`

</td>
<td>

Enables the scale_factor command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-scale-factor`

</td>
<td>

Denies the scale_factor command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-always-on-bottom`

</td>
<td>

Enables the set_always_on_bottom command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-always-on-bottom`

</td>
<td>

Denies the set_always_on_bottom command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-always-on-top`

</td>
<td>

Enables the set_always_on_top command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-always-on-top`

</td>
<td>

Denies the set_always_on_top command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-background-color`

</td>
<td>

Enables the set_background_color command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-background-color`

</td>
<td>

Denies the set_background_color command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-badge-count`

</td>
<td>

Enables the set_badge_count command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-badge-count`

</td>
<td>

Denies the set_badge_count command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-badge-label`

</td>
<td>

Enables the set_badge_label command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-badge-label`

</td>
<td>

Denies the set_badge_label command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-closable`

</td>
<td>

Enables the set_closable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-closable`

</td>
<td>

Denies the set_closable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-content-protected`

</td>
<td>

Enables the set_content_protected command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-content-protected`

</td>
<td>

Denies the set_content_protected command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-cursor-grab`

</td>
<td>

Enables the set_cursor_grab command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-cursor-grab`

</td>
<td>

Denies the set_cursor_grab command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-cursor-icon`

</td>
<td>

Enables the set_cursor_icon command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-cursor-icon`

</td>
<td>

Denies the set_cursor_icon command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-cursor-position`

</td>
<td>

Enables the set_cursor_position command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-cursor-position`

</td>
<td>

Denies the set_cursor_position command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-cursor-visible`

</td>
<td>

Enables the set_cursor_visible command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-cursor-visible`

</td>
<td>

Denies the set_cursor_visible command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-decorations`

</td>
<td>

Enables the set_decorations command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-decorations`

</td>
<td>

Denies the set_decorations command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-effects`

</td>
<td>

Enables the set_effects command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-effects`

</td>
<td>

Denies the set_effects command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-enabled`

</td>
<td>

Enables the set_enabled command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-enabled`

</td>
<td>

Denies the set_enabled command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-focus`

</td>
<td>

Enables the set_focus command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-focus`

</td>
<td>

Denies the set_focus command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-fullscreen`

</td>
<td>

Enables the set_fullscreen command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-fullscreen`

</td>
<td>

Denies the set_fullscreen command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-icon`

</td>
<td>

Enables the set_icon command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-icon`

</td>
<td>

Denies the set_icon command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-ignore-cursor-events`

</td>
<td>

Enables the set_ignore_cursor_events command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-ignore-cursor-events`

</td>
<td>

Denies the set_ignore_cursor_events command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-max-size`

</td>
<td>

Enables the set_max_size command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-max-size`

</td>
<td>

Denies the set_max_size command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-maximizable`

</td>
<td>

Enables the set_maximizable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-maximizable`

</td>
<td>

Denies the set_maximizable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-min-size`

</td>
<td>

Enables the set_min_size command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-min-size`

</td>
<td>

Denies the set_min_size command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-minimizable`

</td>
<td>

Enables the set_minimizable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-minimizable`

</td>
<td>

Denies the set_minimizable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-overlay-icon`

</td>
<td>

Enables the set_overlay_icon command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-overlay-icon`

</td>
<td>

Denies the set_overlay_icon command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-position`

</td>
<td>

Enables the set_position command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-position`

</td>
<td>

Denies the set_position command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-progress-bar`

</td>
<td>

Enables the set_progress_bar command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-progress-bar`

</td>
<td>

Denies the set_progress_bar command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-resizable`

</td>
<td>

Enables the set_resizable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-resizable`

</td>
<td>

Denies the set_resizable command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-shadow`

</td>
<td>

Enables the set_shadow command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-shadow`

</td>
<td>

Denies the set_shadow command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-size`

</td>
<td>

Enables the set_size command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-size`

</td>
<td>

Denies the set_size command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-size-constraints`

</td>
<td>

Enables the set_size_constraints command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-size-constraints`

</td>
<td>

Denies the set_size_constraints command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-skip-taskbar`

</td>
<td>

Enables the set_skip_taskbar command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-skip-taskbar`

</td>
<td>

Denies the set_skip_taskbar command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-theme`

</td>
<td>

Enables the set_theme command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-theme`

</td>
<td>

Denies the set_theme command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-title`

</td>
<td>

Enables the set_title command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-title`

</td>
<td>

Denies the set_title command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-title-bar-style`

</td>
<td>

Enables the set_title_bar_style command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-title-bar-style`

</td>
<td>

Denies the set_title_bar_style command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-set-visible-on-all-workspaces`

</td>
<td>

Enables the set_visible_on_all_workspaces command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-set-visible-on-all-workspaces`

</td>
<td>

Denies the set_visible_on_all_workspaces command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-show`

</td>
<td>

Enables the show command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-show`

</td>
<td>

Denies the show command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-start-dragging`

</td>
<td>

Enables the start_dragging command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-start-dragging`

</td>
<td>

Denies the start_dragging command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-start-resize-dragging`

</td>
<td>

Enables the start_resize_dragging command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-start-resize-dragging`

</td>
<td>

Denies the start_resize_dragging command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-theme`

</td>
<td>

Enables the theme command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-theme`

</td>
<td>

Denies the theme command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-title`

</td>
<td>

Enables the title command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-title`

</td>
<td>

Denies the title command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-toggle-maximize`

</td>
<td>

Enables the toggle_maximize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-toggle-maximize`

</td>
<td>

Denies the toggle_maximize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-unmaximize`

</td>
<td>

Enables the unmaximize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-unmaximize`

</td>
<td>

Denies the unmaximize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:allow-unminimize`

</td>
<td>

Enables the unminimize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:window:deny-unminimize`

</td>
<td>

Denies the unminimize command without any pre-configured scope.

</td>
</tr>
</table>
