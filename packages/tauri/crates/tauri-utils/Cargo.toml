[package]
name = "tauri-utils"
version = "2.6.0"
description = "Utilities for <PERSON>ri"
exclude = ["CHANGELOG.md", "/target"]
readme = "README.md"
authors.workspace = true
homepage.workspace = true
repository.workspace = true
categories.workspace = true
license.workspace = true
edition.workspace = true
rust-version.workspace = true

[dependencies]
serde = { version = "1", features = ["derive"] }
serde_json = "1"
anyhow = "1"
thiserror = "2"
phf = { version = "0.11", features = ["macros"] }
brotli = { version = "8", optional = true, default-features = false, features = [
  "std",
] }
url = { version = "2", features = ["serde"] }
html5ever = { version = "0.29", optional = true }
kuchiki = { package = "kuchikiki", version = "0.8.8-speedreader", optional = true }
proc-macro2 = { version = "1", optional = true }
quote = { version = "1", optional = true }
# Our code requires at least 0.8.21 so don't change this to 0.8
schemars = { version = "0.8.21", features = ["url", "uuid1"], optional = true }
serde_with = "3"
aes-gcm = { version = "0.10", optional = true }
getrandom = { version = "0.3", optional = true, features = ["std"] }
serialize-to-javascript = { version = "=0.1.1", optional = true }
ctor = "0.2"
json5 = { version = "0.4", optional = true }
toml = { version = "0.8", features = ["parse"] }
json-patch = "3.0"
# Our code requires at least 0.3.1
glob = "0.3.1"
urlpattern = "0.3"
regex = "1"
walkdir = { version = "2", optional = true }
memchr = "2"
semver = "1"
infer = "0.19"
dunce = "1"
log = "0.4.21"
cargo_metadata = { version = "0.19", optional = true }
serde-untagged = "0.1"
uuid = { version = "1", features = ["serde"] }
http = "1"

[target."cfg(target_os = \"macos\")".dependencies]
swift-rs = { version = "1", optional = true, features = ["build"] }

[dev-dependencies]
getrandom = { version = "0.3", features = ["std"] }
serial_test = "3"

[features]
build = [
  "proc-macro2",
  "quote",
  "cargo_metadata",
  "schema",
  "swift-rs",
  "html-manipulation",
]
compression = ["brotli"]
schema = ["schemars"]
isolation = ["aes-gcm", "getrandom", "serialize-to-javascript"]
process-relaunch-dangerous-allow-symlink-macos = []
config-json5 = ["json5"]
config-toml = []
resources = ["walkdir"]
html-manipulation = ["dep:html5ever", "dep:kuchiki"]
