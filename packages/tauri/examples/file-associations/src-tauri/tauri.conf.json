{"$schema": "../../../crates/tauri-cli/schema.json", "identifier": "com.tauri.dev-file-associations-demo", "build": {"frontendDist": ["../index.html"]}, "app": {"security": {"csp": "default-src 'self'"}}, "bundle": {"active": true, "targets": "all", "icon": ["../../.icons/32x32.png", "../../.icons/128x128.png", "../../.icons/<EMAIL>", "../../.icons/icon.icns", "../../.icons/icon.ico"], "fileAssociations": [{"ext": ["png"], "mimeType": "image/png", "rank": "<PERSON><PERSON><PERSON>"}, {"ext": ["jpg", "jpeg"], "mimeType": "image/jpeg", "rank": "Alternate"}, {"ext": ["gif"], "mimeType": "image/gif", "rank": "Owner"}]}}