// Copyright 2019-2024 Tauri Programme within The Commons Conservancy
// SPDX-License-Identifier: Apache-2.0
// SPDX-License-Identifier: MIT

use crate::dpi::{PhysicalPosition, PhysicalRect, PhysicalSize};

/// Monitor descriptor.
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Monitor {
  /// A human-readable name of the monitor.
  /// `None` if the monitor doesn't exist anymore.
  pub name: Option<String>,
  /// The monitor's resolution.
  pub size: PhysicalSize<u32>,
  /// The top-left corner position of the monitor relative to the larger full screen area.
  pub position: PhysicalPosition<i32>,
  /// The monitor's work_area.
  pub work_area: PhysicalRect<i32, u32>,
  /// Returns the scale factor that can be used to map logical pixels to physical pixels, and vice versa.
  pub scale_factor: f64,
}
