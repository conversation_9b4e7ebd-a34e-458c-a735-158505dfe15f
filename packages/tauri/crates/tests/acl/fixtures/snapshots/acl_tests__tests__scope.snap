---
source: crates/tests/acl/src/lib.rs
expression: resolved
snapshot_kind: text
---
Resolved {
    allowed_commands: {
        "plugin:fs|move": [
            ResolvedCommand {
                context: Local,
                windows: [
                    Pattern {
                        original: "main",
                        tokens: [
                            Char(
                                'm',
                            ),
                            <PERSON>r(
                                'a',
                            ),
                            <PERSON>r(
                                'i',
                            ),
                            <PERSON>r(
                                'n',
                            ),
                        ],
                        is_recursive: false,
                    },
                ],
                webviews: [],
                scope_id: Some(
                    2,
                ),
            },
        ],
        "plugin:fs|read_dir": [
            ResolvedCommand {
                context: Local,
                windows: [
                    Pattern {
                        original: "main",
                        tokens: [
                            Char(
                                'm',
                            ),
                            Char(
                                'a',
                            ),
                            Char(
                                'i',
                            ),
                            Char(
                                'n',
                            ),
                        ],
                        is_recursive: false,
                    },
                ],
                webviews: [],
                scope_id: None,
            },
            ResolvedCommand {
                context: Local,
                windows: [
                    Pattern {
                        original: "main",
                        tokens: [
                            Char(
                                'm',
                            ),
                            Char(
                                'a',
                            ),
                            <PERSON><PERSON>(
                                'i',
                            ),
                            <PERSON><PERSON>(
                                'n',
                            ),
                        ],
                        is_recursive: false,
                    },
                ],
                webviews: [],
                scope_id: Some(
                    1,
                ),
            },
            ResolvedCommand {
                context: Local,
                windows: [
                    Pattern {
                        original: "main",
                        tokens: [
                            Char(
                                'm',
                            ),
                            Char(
                                'a',
                            ),
                            Char(
                                'i',
                            ),
                            Char(
                                'n',
                            ),
                        ],
                        is_recursive: false,
                    },
                ],
                webviews: [],
                scope_id: None,
            },
        ],
        "plugin:fs|read_file": [
            ResolvedCommand {
                context: Local,
                windows: [
                    Pattern {
                        original: "main",
                        tokens: [
                            Char(
                                'm',
                            ),
                            Char(
                                'a',
                            ),
                            Char(
                                'i',
                            ),
                            Char(
                                'n',
                            ),
                        ],
                        is_recursive: false,
                    },
                ],
                webviews: [],
                scope_id: None,
            },
            ResolvedCommand {
                context: Local,
                windows: [
                    Pattern {
                        original: "main",
                        tokens: [
                            Char(
                                'm',
                            ),
                            Char(
                                'a',
                            ),
                            Char(
                                'i',
                            ),
                            Char(
                                'n',
                            ),
                        ],
                        is_recursive: false,
                    },
                ],
                webviews: [],
                scope_id: Some(
                    1,
                ),
            },
        ],
    },
    denied_commands: {},
    command_scope: {
        1: ResolvedScope {
            allow: [
                Map(
                    {
                        "path": String(
                            "$RESOURCE/**",
                        ),
                    },
                ),
                Map(
                    {
                        "path": String(
                            "$RESOURCE",
                        ),
                    },
                ),
            ],
            deny: [],
        },
        2: ResolvedScope {
            allow: [
                Map(
                    {
                        "path": String(
                            "$TEMP/*",
                        ),
                    },
                ),
            ],
            deny: [],
        },
    },
    global_scope: {
        "fs": ResolvedScope {
            allow: [
                Map(
                    {
                        "path": String(
                            "$APP",
                        ),
                    },
                ),
                Map(
                    {
                        "path": String(
                            "$DOWNLOAD",
                        ),
                    },
                ),
                Map(
                    {
                        "path": String(
                            "$DOWNLOAD/**",
                        ),
                    },
                ),
            ],
            deny: [
                Map(
                    {
                        "path": String(
                            "$HOME",
                        ),
                    },
                ),
            ],
        },
    },
}
