{"$schema": "../../crates/tauri-schema-generator/schemas/config.schema.json", "productName": "Isolation", "version": "0.1.0", "identifier": "com.tauri.dev", "build": {"frontendDist": "dist", "beforeDevCommand": "", "beforeBuildCommand": ""}, "app": {"withGlobalTauri": true, "windows": [{"title": "Isolation", "width": 800, "height": 600, "resizable": true, "fullscreen": false}], "security": {"csp": "default-src blob: data: filesystem: ws: wss: http: https: tauri: 'unsafe-eval' 'unsafe-inline' 'self' img-src: 'self'; connect-src ipc: http://ipc.localhost", "pattern": {"use": "isolation", "options": {"dir": "isolation-dist"}}}}, "bundle": {"active": true, "targets": "all", "icon": ["../.icons/32x32.png", "../.icons/128x128.png", "../.icons/<EMAIL>", "../.icons/icon.icns", "../.icons/icon.ico"]}}