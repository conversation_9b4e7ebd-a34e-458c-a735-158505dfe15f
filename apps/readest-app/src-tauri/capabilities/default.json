{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "enables the default permissions", "windows": ["main", "updater", "reader-*"], "permissions": ["core:default", "fs:default", {"identifier": "fs:scope-appconfig-recursive", "allow": [{"path": "$APPCONFIG"}]}, {"identifier": "fs:scope-appdata-recursive", "allow": [{"path": "$APPDATA/Readest/**/*"}], "deny": []}, {"identifier": "fs:allow-appdata-read", "allow": [{"path": "$APPDATA/settings.json"}]}, {"identifier": "fs:allow-appdata-write", "allow": [{"path": "$APPDATA/settings.json"}]}, {"identifier": "fs:allow-cache-read", "allow": [{"path": "$APPCACHE/**/*"}, {"path": "/private/var/mobile/Containers/Data/Application/**/*"}]}, {"identifier": "fs:allow-cache-write", "allow": [{"path": "$APPCACHE/**/*"}, {"path": "/private/var/mobile/Containers/Data/Application/**/*"}]}, {"identifier": "http:default", "allow": [{"url": "https://*.readest.com"}, {"url": "https://github.com/readest/*"}, {"url": "https://*.deepl.com"}, {"url": "https://*.cloudflarestorage.com"}, {"url": "https://edge.microsoft.com"}, {"url": "https://translate.toil.cc"}, {"url": "https://*.microsofttranslator.com"}, {"url": "https://translate.googleapis.com"}]}, "dialog:default", "os:default", "core:window:default", "core:window:allow-close", "core:window:allow-center", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-unmaximize", "core:window:allow-set-size", "core:window:allow-set-focus", "core:window:allow-is-maximized", "core:window:allow-start-dragging", "core:window:allow-toggle-maximize", "core:window:allow-set-fullscreen", "core:window:allow-set-always-on-top", "core:window:allow-destroy", "core:webview:allow-create-webview-window", "core:path:allow-resolve-directory", "log:default", "shell:default", "process:default", "process:allow-exit", "process:allow-restart", "oauth:allow-start", "oauth:allow-cancel", "sign-in-with-apple:default", "opener:default", "haptics:allow-vibrate", "haptics:allow-impact-feedback", "haptics:allow-notification-feedback", "haptics:allow-selection-feedback", "native-bridge:default", "native-tts:default", "deep-link:default"]}