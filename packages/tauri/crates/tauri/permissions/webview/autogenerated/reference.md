## Default Permission

Default permissions for the plugin.

#### This default permission set includes the following:

- `allow-get-all-webviews`
- `allow-webview-position`
- `allow-webview-size`
- `allow-internal-toggle-devtools`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`core:webview:allow-clear-all-browsing-data`

</td>
<td>

Enables the clear_all_browsing_data command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-clear-all-browsing-data`

</td>
<td>

Denies the clear_all_browsing_data command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-create-webview`

</td>
<td>

Enables the create_webview command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-create-webview`

</td>
<td>

Denies the create_webview command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-create-webview-window`

</td>
<td>

Enables the create_webview_window command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-create-webview-window`

</td>
<td>

Denies the create_webview_window command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-get-all-webviews`

</td>
<td>

Enables the get_all_webviews command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-get-all-webviews`

</td>
<td>

Denies the get_all_webviews command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-internal-toggle-devtools`

</td>
<td>

Enables the internal_toggle_devtools command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-internal-toggle-devtools`

</td>
<td>

Denies the internal_toggle_devtools command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-print`

</td>
<td>

Enables the print command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-print`

</td>
<td>

Denies the print command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-reparent`

</td>
<td>

Enables the reparent command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-reparent`

</td>
<td>

Denies the reparent command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-set-webview-auto-resize`

</td>
<td>

Enables the set_webview_auto_resize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-set-webview-auto-resize`

</td>
<td>

Denies the set_webview_auto_resize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-set-webview-background-color`

</td>
<td>

Enables the set_webview_background_color command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-set-webview-background-color`

</td>
<td>

Denies the set_webview_background_color command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-set-webview-focus`

</td>
<td>

Enables the set_webview_focus command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-set-webview-focus`

</td>
<td>

Denies the set_webview_focus command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-set-webview-position`

</td>
<td>

Enables the set_webview_position command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-set-webview-position`

</td>
<td>

Denies the set_webview_position command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-set-webview-size`

</td>
<td>

Enables the set_webview_size command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-set-webview-size`

</td>
<td>

Denies the set_webview_size command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-set-webview-zoom`

</td>
<td>

Enables the set_webview_zoom command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-set-webview-zoom`

</td>
<td>

Denies the set_webview_zoom command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-webview-close`

</td>
<td>

Enables the webview_close command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-webview-close`

</td>
<td>

Denies the webview_close command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-webview-hide`

</td>
<td>

Enables the webview_hide command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-webview-hide`

</td>
<td>

Denies the webview_hide command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-webview-position`

</td>
<td>

Enables the webview_position command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-webview-position`

</td>
<td>

Denies the webview_position command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-webview-show`

</td>
<td>

Enables the webview_show command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-webview-show`

</td>
<td>

Denies the webview_show command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:allow-webview-size`

</td>
<td>

Enables the webview_size command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:webview:deny-webview-size`

</td>
<td>

Denies the webview_size command without any pre-configured scope.

</td>
</tr>
</table>
