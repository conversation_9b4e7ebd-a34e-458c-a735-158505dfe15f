name: {{app.name}}
options:
  bundleIdPrefix: {{app.identifier}}
  deploymentTarget:
    iOS: {{apple.ios-version}}
fileGroups: [{{join file-groups}}]
configs:
  debug: debug
  release: release
settingGroups:
  app:
    base:
      PRODUCT_NAME: {{app.stylized-name}}
      PRODUCT_BUNDLE_IDENTIFIER: {{app.identifier}}
      {{#if apple.development-team}}
      DEVELOPMENT_TEAM: {{apple.development-team}}
      {{/if}}
targetTemplates:
  app:
    type: application
    sources:
      - path: Sources
    scheme:
      environmentVariables:
        RUST_BACKTRACE: full
        RUST_LOG: info
    settings:
      groups: [app]
targets:
  {{app.name}}_iOS:
    type: application
    platform: iOS
    sources:
      - path: Sources
      - path: Assets.xcassets
      - path: Externals
      - path: {{app.name}}_iOS
      - path: {{app.asset-dir}}
        buildPhase: resources
        type: folder
      {{~#each asset-catalogs}}
      - {{prefix-path this}}{{/each}}
      {{~#each ios-additional-targets}}
      - path: {{prefix-path this}}{{/each}}
      - path: LaunchScreen.storyboard
    info:
      path: {{app.name}}_iOS/Info.plist
      properties:
        LSRequiresIPhoneOS: true
        UILaunchStoryboardName: LaunchScreen
        UIRequiredDeviceCapabilities: [arm64, metal]
        UISupportedInterfaceOrientations:
          - UIInterfaceOrientationPortrait
          - UIInterfaceOrientationLandscapeLeft
          - UIInterfaceOrientationLandscapeRight
        UISupportedInterfaceOrientations~ipad:
          - UIInterfaceOrientationPortrait
          - UIInterfaceOrientationPortraitUpsideDown
          - UIInterfaceOrientationLandscapeLeft
          - UIInterfaceOrientationLandscapeRight
        CFBundleShortVersionString: {{apple.bundle-version-short}}
        CFBundleVersion: "{{apple.bundle-version}}"
        {{~#each apple.plist-pairs}}
        {{this.key}}: {{this.value}}{{/each}}
    entitlements:
      path: {{app.name}}_iOS/{{app.name}}_iOS.entitlements
    scheme:
      environmentVariables:
        RUST_BACKTRACE: full
        RUST_LOG: info
      {{~#if ios-command-line-arguments}}
      commandLineArguments:
      {{~#each ios-command-line-arguments}}
        "{{this}}": true
      {{/each}}{{~/if}}
    settings:
      base:
        ENABLE_BITCODE: false
        ARCHS: [{{join ios-valid-archs}}]
        VALID_ARCHS: {{~#each ios-valid-archs}} {{this}} {{/each}}
        LIBRARY_SEARCH_PATHS[arch=x86_64]: $(inherited) $(PROJECT_DIR)/Externals/x86_64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)
        LIBRARY_SEARCH_PATHS[arch=arm64]: $(inherited) $(PROJECT_DIR)/Externals/arm64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)
        ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES: true
        EXCLUDED_ARCHS[sdk=iphoneos*]: x86_64
      groups: [app]
    dependencies:
      - framework: {{ lib-output-file-name }}
        embed: false
      {{~#each ios-libraries}}
      - framework: {{this}}
        embed: false{{/each}}{{#if ios-vendor-frameworks}}{{~#each ios-vendor-frameworks}}
      - framework: {{this}}{{/each}}{{/if}}{{#if ios-vendor-sdks}}{{~#each ios-vendor-sdks}}
      - sdk: {{prefix-path this}}{{/each}}{{/if}}
      - sdk: CoreGraphics.framework
      - sdk: Metal.framework
      - sdk: MetalKit.framework
      - sdk: QuartzCore.framework
      - sdk: Security.framework
      - sdk: UIKit.framework{{#if this.ios-frameworks}}{{~#each ios-frameworks}}
      - sdk: {{this}}.framework{{/each}}{{/if}}
      - sdk: WebKit.framework
    preBuildScripts:
      {{~#each ios-pre-build-scripts}}{{#if this.path}}
      - path {{this.path}}{{/if}}{{#if this.script}}
      - script: {{this.script}}{{/if}}{{#if this.name}}
        name: {{this.name}}{{/if}}{{#if this.input-files}}
        inputFiles: {{~#each this.input-files}}
          - {{this}}{{/each}}{{/if}}{{#if this.output-files}}
        outputFiles: {{~#each this.output-files}}
          - {{this}}{{/each}}{{/if}}{{#if this.input-file-lists}}
        inputFileLists: {{~#each this.output-files}}
          - {{this}}{{/each}}{{/if}}{{#if this.output-file-lists}}
        outputFileLists: {{~#each this.output-files}}
          - {{this}}{{/each}}{{/if}}{{#if this.shell}}
        shell: {{this.shell}}{{/if}}{{#if this.show-env-vars}}
        showEnvVars: {{this.show_env_vars}}{{/if}}{{#if this.run-only-when-installing}}
        runOnlyWhenInstalling: {{this.run-only-when-installing}}{{/if}}{{#if this.based-on-dependency-analysis}}
        basedOnDependencyAnalysis: {{this.based-on-dependency-analysis}}{{/if}}{{#if this.discovered-dependency-file}}
        discoveredDependencyFile: {{this.discovered-dependency-file}}{{/if}}
      {{~/each}}

      - script: {{ tauri-binary }} {{ tauri-binary-args-str }} -v --platform ${PLATFORM_DISPLAY_NAME:?} --sdk-root ${SDKROOT:?} --framework-search-paths "${FRAMEWORK_SEARCH_PATHS:?}" --header-search-paths "${HEADER_SEARCH_PATHS:?}" --gcc-preprocessor-definitions "${GCC_PREPROCESSOR_DEFINITIONS:-}" --configuration ${CONFIGURATION:?} ${FORCE_COLOR} ${ARCHS:?}
        name: Build Rust Code
        basedOnDependencyAnalysis: false
        outputFiles:
          - $(SRCROOT)/Externals/x86_64/${CONFIGURATION}/{{ lib-output-file-name }}
          - $(SRCROOT)/Externals/arm64/${CONFIGURATION}/{{ lib-output-file-name }}
    {{~#if ios-post-compile-scripts}}
    postCompileScripts:
      {{~#each ios-post-compile-scripts}}{{#if this.path}}
      - path {{this.path}}{{/if}}{{#if this.script}}
      - script: {{this.script}}{{/if}}{{#if this.name}}
        name: {{this.name}}{{/if}}{{#if this.input-files}}
        inputFiles: {{~#each this.input-files}}
          - {{this}}{{/each}}{{/if}}{{#if this.output-files}}
        outputFiles: {{~#each this.output-files}}
          - {{this}}{{/each}}{{/if}}{{#if this.input-file-lists}}
        inputFileLists: {{~#each this.output-files}}
          - {{this}}{{/each}}{{/if}}{{#if this.output-file-lists}}
        outputFileLists: {{~#each this.output-files}}
          - {{this}}{{/each}}{{/if}}{{#if this.shell}}
        shell: {{this.shell}}{{/if}}{{#if this.show-env-vars}}
        showEnvVars: {{this.show_env_vars}}{{/if}}{{#if this.run-only-when-installing}}
        runOnlyWhenInstalling: {{this.run-only-when-installing}}{{/if}}{{#if this.based-on-dependency-analysis}}
        basedOnDependencyAnalysis: {{this.based-on-dependency-analysis}}{{/if}}{{#if this.discovered-dependency-file}}
        discoveredDependencyFile: {{this.discovered-dependency-file}}{{/if}}
      {{~/each~}}
    {{~/if~}}
    {{~#if ios-post-build-scripts}}
    postBuildScripts:
      {{~#each ios-post-build-scripts}}{{#if this.path}}
      - path {{this.path}}{{/if}}{{#if this.script}}
      - script: {{this.script}}{{/if}}{{#if this.name}}
        name: {{this.name}}{{/if}}{{#if this.input-files}}
        inputFiles: {{~#each this.input-files}}
          - {{this}}{{/each}}{{/if}}{{#if this.output-files}}
        outputFiles: {{~#each this.output-files}}
          - {{this}}{{/each}}{{/if}}{{#if this.input-file-lists}}
        inputFileLists: {{~#each this.output-files}}
          - {{this}}{{/each}}{{/if}}{{#if this.output-file-lists}}
        outputFileLists: {{~#each this.output-files}}
          - {{this}}{{/each}}{{/if}}{{#if this.shell}}
        shell: {{this.shell}}{{/if}}{{#if this.show-env-vars}}
        showEnvVars: {{this.show_env_vars}}{{/if}}{{#if this.run-only-when-installing}}
        runOnlyWhenInstalling: {{this.run-only-when-installing}}{{/if}}{{#if this.based-on-dependency-analysis}}
        basedOnDependencyAnalysis: {{this.based-on-dependency-analysis}}{{/if}}{{#if this.discovered-dependency-file}}
        discoveredDependencyFile: {{this.discovered-dependency-file}}{{/if}}
      {{~/each~}}
    {{~/if}}
