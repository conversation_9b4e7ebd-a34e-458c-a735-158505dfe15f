## Default Permission

Default permissions for the plugin, which enables all commands.

#### This default permission set includes the following:

- `allow-new`
- `allow-append`
- `allow-prepend`
- `allow-insert`
- `allow-remove`
- `allow-remove-at`
- `allow-items`
- `allow-get`
- `allow-popup`
- `allow-create-default`
- `allow-set-as-app-menu`
- `allow-set-as-window-menu`
- `allow-text`
- `allow-set-text`
- `allow-is-enabled`
- `allow-set-enabled`
- `allow-set-accelerator`
- `allow-set-as-windows-menu-for-nsapp`
- `allow-set-as-help-menu-for-nsapp`
- `allow-is-checked`
- `allow-set-checked`
- `allow-set-icon`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`core:menu:allow-append`

</td>
<td>

Enables the append command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-append`

</td>
<td>

Denies the append command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-create-default`

</td>
<td>

Enables the create_default command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-create-default`

</td>
<td>

Denies the create_default command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-get`

</td>
<td>

Enables the get command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-get`

</td>
<td>

Denies the get command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-insert`

</td>
<td>

Enables the insert command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-insert`

</td>
<td>

Denies the insert command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-is-checked`

</td>
<td>

Enables the is_checked command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-is-checked`

</td>
<td>

Denies the is_checked command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-is-enabled`

</td>
<td>

Enables the is_enabled command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-is-enabled`

</td>
<td>

Denies the is_enabled command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-items`

</td>
<td>

Enables the items command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-items`

</td>
<td>

Denies the items command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-new`

</td>
<td>

Enables the new command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-new`

</td>
<td>

Denies the new command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-popup`

</td>
<td>

Enables the popup command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-popup`

</td>
<td>

Denies the popup command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-prepend`

</td>
<td>

Enables the prepend command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-prepend`

</td>
<td>

Denies the prepend command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-remove`

</td>
<td>

Enables the remove command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-remove`

</td>
<td>

Denies the remove command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-remove-at`

</td>
<td>

Enables the remove_at command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-remove-at`

</td>
<td>

Denies the remove_at command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-set-accelerator`

</td>
<td>

Enables the set_accelerator command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-set-accelerator`

</td>
<td>

Denies the set_accelerator command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-set-as-app-menu`

</td>
<td>

Enables the set_as_app_menu command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-set-as-app-menu`

</td>
<td>

Denies the set_as_app_menu command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-set-as-help-menu-for-nsapp`

</td>
<td>

Enables the set_as_help_menu_for_nsapp command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-set-as-help-menu-for-nsapp`

</td>
<td>

Denies the set_as_help_menu_for_nsapp command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-set-as-window-menu`

</td>
<td>

Enables the set_as_window_menu command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-set-as-window-menu`

</td>
<td>

Denies the set_as_window_menu command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-set-as-windows-menu-for-nsapp`

</td>
<td>

Enables the set_as_windows_menu_for_nsapp command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-set-as-windows-menu-for-nsapp`

</td>
<td>

Denies the set_as_windows_menu_for_nsapp command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-set-checked`

</td>
<td>

Enables the set_checked command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-set-checked`

</td>
<td>

Denies the set_checked command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-set-enabled`

</td>
<td>

Enables the set_enabled command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-set-enabled`

</td>
<td>

Denies the set_enabled command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-set-icon`

</td>
<td>

Enables the set_icon command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-set-icon`

</td>
<td>

Denies the set_icon command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-set-text`

</td>
<td>

Enables the set_text command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-set-text`

</td>
<td>

Denies the set_text command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:allow-text`

</td>
<td>

Enables the text command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:menu:deny-text`

</td>
<td>

Denies the text command without any pre-configured scope.

</td>
</tr>
</table>
