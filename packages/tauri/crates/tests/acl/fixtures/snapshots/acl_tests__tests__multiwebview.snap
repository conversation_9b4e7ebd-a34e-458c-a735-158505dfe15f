---
source: crates/tests/acl/src/lib.rs
expression: resolved
---
Resolved {
    allowed_commands: {
        "plugin:ping|ping": [
            ResolvedCommand {
                context: Local,
                windows: [
                    Pattern {
                        original: "main",
                        tokens: [
                            Char(
                                'm',
                            ),
                            <PERSON><PERSON>(
                                'a',
                            ),
                            <PERSON><PERSON>(
                                'i',
                            ),
                            <PERSON><PERSON>(
                                'n',
                            ),
                        ],
                        is_recursive: false,
                    },
                ],
                webviews: [
                    Pattern {
                        original: "child1",
                        tokens: [
                            <PERSON><PERSON>(
                                'c',
                            ),
                            Char(
                                'h',
                            ),
                            Char(
                                'i',
                            ),
                            Char(
                                'l',
                            ),
                            Char(
                                'd',
                            ),
                            Char(
                                '1',
                            ),
                        ],
                        is_recursive: false,
                    },
                    Pattern {
                        original: "child2",
                        tokens: [
                            Char(
                                'c',
                            ),
                            Char(
                                'h',
                            ),
                            Char(
                                'i',
                            ),
                            Char(
                                'l',
                            ),
                            Char(
                                'd',
                            ),
                            Char(
                                '2',
                            ),
                        ],
                        is_recursive: false,
                    },
                ],
                scope_id: None,
            },
        ],
    },
    denied_commands: {},
    command_scope: {},
    global_scope: {},
}
