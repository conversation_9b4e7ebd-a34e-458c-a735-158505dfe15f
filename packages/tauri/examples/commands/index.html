<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tauri</title>
  </head>

  <body>
    <h1>Tauri Commands</h1>
    <div>Response: <span id="response"></span></div>
    <div>Without Args: <span id="response-optional"></span></div>
    <div id="container"></div>
    <script>
      function runCommand(commandName, args, optional) {
        const id = optional ? '#response-optional' : '#response'
        const result = document.querySelector(id)
        window.__TAURI__.core
          .invoke(commandName, args)
          .then((response) => {
            const val =
              response instanceof ArrayBuffer
                ? new TextDecoder().decode(response)
                : response
            result.innerText = `Ok(${val})`
          })
          .catch((error) => {
            result.innerText = `Err(${error})`
          })
      }

      const container = document.querySelector('#container')
      const commands = [
        { name: 'borrow_cmd' },
        { name: 'raw_request' },
        { name: 'window_label' },
        { name: 'simple_command' },
        { name: 'stateful_command' },
        { name: 'async_simple_command' },
        { name: 'async_simple_command_snake' },
        { name: 'future_simple_command' },
        { name: 'async_stateful_command' },
        { name: 'simple_command_with_result' },
        // snake
        { name: 'future_simple_command_snake' },
        { name: 'future_simple_command_with_return_snake' },
        { name: 'future_simple_command_with_result_snake' },
        { name: 'force_async_snake' },
        { name: 'force_async_with_result_snake' },
        { name: 'simple_command_with_result_snake' },
        { name: 'stateful_command_with_result_snake' },
        // state
        { name: 'stateful_command_with_result' },
        { name: 'async_simple_command_with_result' },
        { name: 'future_simple_command_with_return' },
        { name: 'future_simple_command_with_result' },
        { name: 'async_stateful_command_with_result' },
        { name: 'command_arguments_wild' },
        {
          name: 'command_arguments_struct',
          args: { person: { name: 'ferris', age: 6 } }
        },
        {
          name: 'command_arguments_tuple_struct',
          args: { inlinePerson: ['ferris', 6] }
        }
      ]

      for (const command of commands) {
        const { name } = command
        const args = command.args ?? {
          [name.endsWith('snake') ? 'the_argument' : 'theArgument']: 'value'
        }
        const button = document.createElement('button')
        button.innerHTML = `Run ${name}`
        button.addEventListener('click', function () {
          runCommand(name, args, false)
          runCommand(name, Object.create(null), true)
        })
        container.appendChild(button)
      }
    </script>
  </body>
</html>
