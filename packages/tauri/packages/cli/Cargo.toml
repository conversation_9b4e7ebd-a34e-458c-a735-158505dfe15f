[package]
edition = "2021"
name = "tauri-cli-node"
version = "0.0.0"

[lib]
crate-type = ["cdylib"]

[dependencies]
napi = "3"
napi-derive = "3"
tauri-cli = { path = "../../crates/tauri-cli", default-features = false }
log = "0.4.21"

[build-dependencies]
napi-build = "2.2"

[features]
default = ["tauri-cli/default"]
native-tls = ["tauri-cli/native-tls"]
native-tls-vendored = ["tauri-cli/native-tls-vendored"]
