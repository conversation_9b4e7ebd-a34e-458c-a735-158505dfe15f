var __TAURI_IIFE__=function(e){"use strict";function n(e,n,t,i){if("a"===t&&!i)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof n?e!==n||!i:!n.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===t?i:"a"===t?i.call(e):i?i.value:n.get(e)}function t(e,n,t,i,r){if("m"===i)throw new TypeError("Private method is not writable");if("a"===i&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof n?e!==n||!r:!n.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?r.call(e,t):r?r.value=t:n.set(e,t),t}var i,r,s,a,l;"function"==typeof SuppressedError&&SuppressedError;const o="__TAURI_TO_IPC_KEY__";function u(e,n=!1){return window.__TAURI_INTERNALS__.transformCallback(e,n)}class c{constructor(e){i.set(this,void 0),r.set(this,0),s.set(this,[]),a.set(this,void 0),t(this,i,e||(()=>{}),"f"),this.id=u(e=>{const l=e.index;if("end"in e)return void(l==n(this,r,"f")?this.cleanupCallback():t(this,a,l,"f"));const o=e.message;if(l==n(this,r,"f")){for(n(this,i,"f").call(this,o),t(this,r,n(this,r,"f")+1,"f");n(this,r,"f")in n(this,s,"f");){const e=n(this,s,"f")[n(this,r,"f")];n(this,i,"f").call(this,e),delete n(this,s,"f")[n(this,r,"f")],t(this,r,n(this,r,"f")+1,"f")}n(this,r,"f")===n(this,a,"f")&&this.cleanupCallback()}else n(this,s,"f")[l]=o})}cleanupCallback(){window.__TAURI_INTERNALS__.unregisterCallback(this.id)}set onmessage(e){t(this,i,e,"f")}get onmessage(){return n(this,i,"f")}[(i=new WeakMap,r=new WeakMap,s=new WeakMap,a=new WeakMap,o)](){return`__CHANNEL__:${this.id}`}toJSON(){return this[o]()}}class d{constructor(e,n,t){this.plugin=e,this.event=n,this.channelId=t}async unregister(){return p(`plugin:${this.plugin}|remove_listener`,{event:this.event,channelId:this.channelId})}}async function p(e,n={},t){return window.__TAURI_INTERNALS__.invoke(e,n,t)}class h{get rid(){return n(this,l,"f")}constructor(e){l.set(this,void 0),t(this,l,e,"f")}async close(){return p("plugin:resources|close",{rid:this.rid})}}l=new WeakMap;var w=Object.freeze({__proto__:null,Channel:c,PluginListener:d,Resource:h,SERIALIZE_TO_IPC_FN:o,addPluginListener:async function(e,n,t){const i=new c(t);return p(`plugin:${e}|registerListener`,{event:n,handler:i}).then(()=>new d(e,n,i.id))},checkPermissions:async function(e){return p(`plugin:${e}|check_permissions`)},convertFileSrc:function(e,n="asset"){return window.__TAURI_INTERNALS__.convertFileSrc(e,n)},invoke:p,isTauri:function(){return!!(globalThis||window).isTauri},requestPermissions:async function(e){return p(`plugin:${e}|request_permissions`)},transformCallback:u});class _ extends h{constructor(e){super(e)}static async new(e,n,t){return p("plugin:image|new",{rgba:y(e),width:n,height:t}).then(e=>new _(e))}static async fromBytes(e){return p("plugin:image|from_bytes",{bytes:y(e)}).then(e=>new _(e))}static async fromPath(e){return p("plugin:image|from_path",{path:e}).then(e=>new _(e))}async rgba(){return p("plugin:image|rgba",{rid:this.rid}).then(e=>new Uint8Array(e))}async size(){return p("plugin:image|size",{rid:this.rid})}}function y(e){return null==e?null:"string"==typeof e?e:e instanceof _?e.rid:e}var g,b=Object.freeze({__proto__:null,Image:_,transformImage:y});!function(e){e.Nsis="nsis",e.Msi="msi",e.Deb="deb",e.Rpm="rpm",e.AppImage="appimage",e.App="app"}(g||(g={}));var m=Object.freeze({__proto__:null,get BundleType(){return g},defaultWindowIcon:async function(){return p("plugin:app|default_window_icon").then(e=>e?new _(e):null)},fetchDataStoreIdentifiers:async function(){return p("plugin:app|fetch_data_store_identifiers")},getBundleType:async function(){return p("plugin:app|bundle_type")},getIdentifier:async function(){return p("plugin:app|identifier")},getName:async function(){return p("plugin:app|name")},getTauriVersion:async function(){return p("plugin:app|tauri_version")},getVersion:async function(){return p("plugin:app|version")},hide:async function(){return p("plugin:app|app_hide")},removeDataStore:async function(e){return p("plugin:app|remove_data_store",{uuid:e})},setDockVisibility:async function(e){return p("plugin:app|set_dock_visibility",{visible:e})},setTheme:async function(e){return p("plugin:app|set_app_theme",{theme:e})},show:async function(){return p("plugin:app|app_show")}});class v{constructor(...e){this.type="Logical",1===e.length?"Logical"in e[0]?(this.width=e[0].Logical.width,this.height=e[0].Logical.height):(this.width=e[0].width,this.height=e[0].height):(this.width=e[0],this.height=e[1])}toPhysical(e){return new f(this.width*e,this.height*e)}[o](){return{width:this.width,height:this.height}}toJSON(){return this[o]()}}class f{constructor(...e){this.type="Physical",1===e.length?"Physical"in e[0]?(this.width=e[0].Physical.width,this.height=e[0].Physical.height):(this.width=e[0].width,this.height=e[0].height):(this.width=e[0],this.height=e[1])}toLogical(e){return new v(this.width/e,this.height/e)}[o](){return{width:this.width,height:this.height}}toJSON(){return this[o]()}}class k{constructor(e){this.size=e}toLogical(e){return this.size instanceof v?this.size:this.size.toLogical(e)}toPhysical(e){return this.size instanceof f?this.size:this.size.toPhysical(e)}[o](){return{[`${this.size.type}`]:{width:this.size.width,height:this.size.height}}}toJSON(){return this[o]()}}class A{constructor(...e){this.type="Logical",1===e.length?"Logical"in e[0]?(this.x=e[0].Logical.x,this.y=e[0].Logical.y):(this.x=e[0].x,this.y=e[0].y):(this.x=e[0],this.y=e[1])}toPhysical(e){return new T(this.x*e,this.y*e)}[o](){return{x:this.x,y:this.y}}toJSON(){return this[o]()}}class T{constructor(...e){this.type="Physical",1===e.length?"Physical"in e[0]?(this.x=e[0].Physical.x,this.y=e[0].Physical.y):(this.x=e[0].x,this.y=e[0].y):(this.x=e[0],this.y=e[1])}toLogical(e){return new A(this.x/e,this.y/e)}[o](){return{x:this.x,y:this.y}}toJSON(){return this[o]()}}class I{constructor(e){this.position=e}toLogical(e){return this.position instanceof A?this.position:this.position.toLogical(e)}toPhysical(e){return this.position instanceof T?this.position:this.position.toPhysical(e)}[o](){return{[`${this.position.type}`]:{x:this.position.x,y:this.position.y}}}toJSON(){return this[o]()}}var E,R=Object.freeze({__proto__:null,LogicalPosition:A,LogicalSize:v,PhysicalPosition:T,PhysicalSize:f,Position:I,Size:k});async function D(e,n){window.__TAURI_EVENT_PLUGIN_INTERNALS__.unregisterListener(e,n),await p("plugin:event|unlisten",{event:e,eventId:n})}async function S(e,n,t){var i;const r="string"==typeof(null==t?void 0:t.target)?{kind:"AnyLabel",label:t.target}:null!==(i=null==t?void 0:t.target)&&void 0!==i?i:{kind:"Any"};return p("plugin:event|listen",{event:e,target:r,handler:u(n)}).then(n=>async()=>D(e,n))}async function N(e,n,t){return S(e,t=>{D(e,t.id),n(t)},t)}async function L(e,n){await p("plugin:event|emit",{event:e,payload:n})}async function C(e,n,t){const i="string"==typeof e?{kind:"AnyLabel",label:e}:e;await p("plugin:event|emit_to",{target:i,event:n,payload:t})}!function(e){e.WINDOW_RESIZED="tauri://resize",e.WINDOW_MOVED="tauri://move",e.WINDOW_CLOSE_REQUESTED="tauri://close-requested",e.WINDOW_DESTROYED="tauri://destroyed",e.WINDOW_FOCUS="tauri://focus",e.WINDOW_BLUR="tauri://blur",e.WINDOW_SCALE_FACTOR_CHANGED="tauri://scale-change",e.WINDOW_THEME_CHANGED="tauri://theme-changed",e.WINDOW_CREATED="tauri://window-created",e.WEBVIEW_CREATED="tauri://webview-created",e.DRAG_ENTER="tauri://drag-enter",e.DRAG_OVER="tauri://drag-over",e.DRAG_DROP="tauri://drag-drop",e.DRAG_LEAVE="tauri://drag-leave"}(E||(E={}));var x,P,z,W=Object.freeze({__proto__:null,get TauriEvent(){return E},emit:L,emitTo:C,listen:S,once:N});function O(e){var n;if("items"in e)e.items=null===(n=e.items)||void 0===n?void 0:n.map(e=>"rid"in e?e:O(e));else if("action"in e&&e.action){const n=new c;return n.onmessage=e.action,delete e.action,{...e,handler:n}}return e}async function U(e,n){const t=new c;if(n&&"object"==typeof n&&("action"in n&&n.action&&(t.onmessage=n.action,delete n.action),"item"in n&&n.item&&"object"==typeof n.item&&"About"in n.item&&n.item.About&&"object"==typeof n.item.About&&"icon"in n.item.About&&n.item.About.icon&&(n.item.About.icon=y(n.item.About.icon)),"icon"in n&&n.icon&&(n.icon=y(n.icon)),"items"in n&&n.items)){function i(e){var n;return"rid"in e?[e.rid,e.kind]:("item"in e&&"object"==typeof e.item&&(null===(n=e.item.About)||void 0===n?void 0:n.icon)&&(e.item.About.icon=y(e.item.About.icon)),"icon"in e&&e.icon&&(e.icon=y(e.icon)),"items"in e&&e.items&&(e.items=e.items.map(i)),O(e))}n.items=n.items.map(i)}return p("plugin:menu|new",{kind:e,options:n,handler:t})}class F extends h{get id(){return n(this,x,"f")}get kind(){return n(this,P,"f")}constructor(e,n,i){super(e),x.set(this,void 0),P.set(this,void 0),t(this,x,n,"f"),t(this,P,i,"f")}}x=new WeakMap,P=new WeakMap;class M extends F{constructor(e,n){super(e,n,"MenuItem")}static async new(e){return U("MenuItem",e).then(([e,n])=>new M(e,n))}async text(){return p("plugin:menu|text",{rid:this.rid,kind:this.kind})}async setText(e){return p("plugin:menu|set_text",{rid:this.rid,kind:this.kind,text:e})}async isEnabled(){return p("plugin:menu|is_enabled",{rid:this.rid,kind:this.kind})}async setEnabled(e){return p("plugin:menu|set_enabled",{rid:this.rid,kind:this.kind,enabled:e})}async setAccelerator(e){return p("plugin:menu|set_accelerator",{rid:this.rid,kind:this.kind,accelerator:e})}}class B extends F{constructor(e,n){super(e,n,"Check")}static async new(e){return U("Check",e).then(([e,n])=>new B(e,n))}async text(){return p("plugin:menu|text",{rid:this.rid,kind:this.kind})}async setText(e){return p("plugin:menu|set_text",{rid:this.rid,kind:this.kind,text:e})}async isEnabled(){return p("plugin:menu|is_enabled",{rid:this.rid,kind:this.kind})}async setEnabled(e){return p("plugin:menu|set_enabled",{rid:this.rid,kind:this.kind,enabled:e})}async setAccelerator(e){return p("plugin:menu|set_accelerator",{rid:this.rid,kind:this.kind,accelerator:e})}async isChecked(){return p("plugin:menu|is_checked",{rid:this.rid})}async setChecked(e){return p("plugin:menu|set_checked",{rid:this.rid,checked:e})}}!function(e){e.Add="Add",e.Advanced="Advanced",e.Bluetooth="Bluetooth",e.Bookmarks="Bookmarks",e.Caution="Caution",e.ColorPanel="ColorPanel",e.ColumnView="ColumnView",e.Computer="Computer",e.EnterFullScreen="EnterFullScreen",e.Everyone="Everyone",e.ExitFullScreen="ExitFullScreen",e.FlowView="FlowView",e.Folder="Folder",e.FolderBurnable="FolderBurnable",e.FolderSmart="FolderSmart",e.FollowLinkFreestanding="FollowLinkFreestanding",e.FontPanel="FontPanel",e.GoLeft="GoLeft",e.GoRight="GoRight",e.Home="Home",e.IChatTheater="IChatTheater",e.IconView="IconView",e.Info="Info",e.InvalidDataFreestanding="InvalidDataFreestanding",e.LeftFacingTriangle="LeftFacingTriangle",e.ListView="ListView",e.LockLocked="LockLocked",e.LockUnlocked="LockUnlocked",e.MenuMixedState="MenuMixedState",e.MenuOnState="MenuOnState",e.MobileMe="MobileMe",e.MultipleDocuments="MultipleDocuments",e.Network="Network",e.Path="Path",e.PreferencesGeneral="PreferencesGeneral",e.QuickLook="QuickLook",e.RefreshFreestanding="RefreshFreestanding",e.Refresh="Refresh",e.Remove="Remove",e.RevealFreestanding="RevealFreestanding",e.RightFacingTriangle="RightFacingTriangle",e.Share="Share",e.Slideshow="Slideshow",e.SmartBadge="SmartBadge",e.StatusAvailable="StatusAvailable",e.StatusNone="StatusNone",e.StatusPartiallyAvailable="StatusPartiallyAvailable",e.StatusUnavailable="StatusUnavailable",e.StopProgressFreestanding="StopProgressFreestanding",e.StopProgress="StopProgress",e.TrashEmpty="TrashEmpty",e.TrashFull="TrashFull",e.User="User",e.UserAccounts="UserAccounts",e.UserGroup="UserGroup",e.UserGuest="UserGuest"}(z||(z={}));class V extends F{constructor(e,n){super(e,n,"Icon")}static async new(e){return U("Icon",e).then(([e,n])=>new V(e,n))}async text(){return p("plugin:menu|text",{rid:this.rid,kind:this.kind})}async setText(e){return p("plugin:menu|set_text",{rid:this.rid,kind:this.kind,text:e})}async isEnabled(){return p("plugin:menu|is_enabled",{rid:this.rid,kind:this.kind})}async setEnabled(e){return p("plugin:menu|set_enabled",{rid:this.rid,kind:this.kind,enabled:e})}async setAccelerator(e){return p("plugin:menu|set_accelerator",{rid:this.rid,kind:this.kind,accelerator:e})}async setIcon(e){return p("plugin:menu|set_icon",{rid:this.rid,icon:y(e)})}}class G extends F{constructor(e,n){super(e,n,"Predefined")}static async new(e){return U("Predefined",e).then(([e,n])=>new G(e,n))}async text(){return p("plugin:menu|text",{rid:this.rid,kind:this.kind})}async setText(e){return p("plugin:menu|set_text",{rid:this.rid,kind:this.kind,text:e})}}function j([e,n,t]){switch(t){case"Submenu":return new H(e,n);case"Predefined":return new G(e,n);case"Check":return new B(e,n);case"Icon":return new V(e,n);default:return new M(e,n)}}class H extends F{constructor(e,n){super(e,n,"Submenu")}static async new(e){return U("Submenu",e).then(([e,n])=>new H(e,n))}async text(){return p("plugin:menu|text",{rid:this.rid,kind:this.kind})}async setText(e){return p("plugin:menu|set_text",{rid:this.rid,kind:this.kind,text:e})}async isEnabled(){return p("plugin:menu|is_enabled",{rid:this.rid,kind:this.kind})}async setEnabled(e){return p("plugin:menu|set_enabled",{rid:this.rid,kind:this.kind,enabled:e})}async append(e){return p("plugin:menu|append",{rid:this.rid,kind:this.kind,items:(Array.isArray(e)?e:[e]).map(e=>"rid"in e?[e.rid,e.kind]:e)})}async prepend(e){return p("plugin:menu|prepend",{rid:this.rid,kind:this.kind,items:(Array.isArray(e)?e:[e]).map(e=>"rid"in e?[e.rid,e.kind]:e)})}async insert(e,n){return p("plugin:menu|insert",{rid:this.rid,kind:this.kind,items:(Array.isArray(e)?e:[e]).map(e=>"rid"in e?[e.rid,e.kind]:e),position:n})}async remove(e){return p("plugin:menu|remove",{rid:this.rid,kind:this.kind,item:[e.rid,e.kind]})}async removeAt(e){return p("plugin:menu|remove_at",{rid:this.rid,kind:this.kind,position:e}).then(j)}async items(){return p("plugin:menu|items",{rid:this.rid,kind:this.kind}).then(e=>e.map(j))}async get(e){return p("plugin:menu|get",{rid:this.rid,kind:this.kind,id:e}).then(e=>e?j(e):null)}async popup(e,n){var t;return p("plugin:menu|popup",{rid:this.rid,kind:this.kind,window:null!==(t=null==n?void 0:n.label)&&void 0!==t?t:null,at:e instanceof I?e:e?new I(e):null})}async setAsWindowsMenuForNSApp(){return p("plugin:menu|set_as_windows_menu_for_nsapp",{rid:this.rid})}async setAsHelpMenuForNSApp(){return p("plugin:menu|set_as_help_menu_for_nsapp",{rid:this.rid})}}class $ extends F{constructor(e,n){super(e,n,"Menu")}static async new(e){return U("Menu",e).then(([e,n])=>new $(e,n))}static async default(){return p("plugin:menu|create_default").then(([e,n])=>new $(e,n))}async append(e){return p("plugin:menu|append",{rid:this.rid,kind:this.kind,items:(Array.isArray(e)?e:[e]).map(e=>"rid"in e?[e.rid,e.kind]:e)})}async prepend(e){return p("plugin:menu|prepend",{rid:this.rid,kind:this.kind,items:(Array.isArray(e)?e:[e]).map(e=>"rid"in e?[e.rid,e.kind]:e)})}async insert(e,n){return p("plugin:menu|insert",{rid:this.rid,kind:this.kind,items:(Array.isArray(e)?e:[e]).map(e=>"rid"in e?[e.rid,e.kind]:e),position:n})}async remove(e){return p("plugin:menu|remove",{rid:this.rid,kind:this.kind,item:[e.rid,e.kind]})}async removeAt(e){return p("plugin:menu|remove_at",{rid:this.rid,kind:this.kind,position:e}).then(j)}async items(){return p("plugin:menu|items",{rid:this.rid,kind:this.kind}).then(e=>e.map(j))}async get(e){return p("plugin:menu|get",{rid:this.rid,kind:this.kind,id:e}).then(e=>e?j(e):null)}async popup(e,n){var t;return p("plugin:menu|popup",{rid:this.rid,kind:this.kind,window:null!==(t=null==n?void 0:n.label)&&void 0!==t?t:null,at:e instanceof I?e:e?new I(e):null})}async setAsAppMenu(){return p("plugin:menu|set_as_app_menu",{rid:this.rid}).then(e=>e?new $(e[0],e[1]):null)}async setAsWindowMenu(e){var n;return p("plugin:menu|set_as_window_menu",{rid:this.rid,window:null!==(n=null==e?void 0:e.label)&&void 0!==n?n:null}).then(e=>e?new $(e[0],e[1]):null)}}var q=Object.freeze({__proto__:null,CheckMenuItem:B,IconMenuItem:V,Menu:$,MenuItem:M,get NativeIcon(){return z},PredefinedMenuItem:G,Submenu:H,itemFromKind:j});function J(){var e,n;window.__TAURI_INTERNALS__=null!==(e=window.__TAURI_INTERNALS__)&&void 0!==e?e:{},window.__TAURI_EVENT_PLUGIN_INTERNALS__=null!==(n=window.__TAURI_EVENT_PLUGIN_INTERNALS__)&&void 0!==n?n:{}}var Q,Z=Object.freeze({__proto__:null,clearMocks:function(){"object"==typeof window.__TAURI_INTERNALS__&&(delete window.__TAURI_INTERNALS__.invoke,delete window.__TAURI_INTERNALS__.transformCallback,delete window.__TAURI_INTERNALS__.unregisterCallback,delete window.__TAURI_INTERNALS__.runCallback,delete window.__TAURI_INTERNALS__.callbacks,delete window.__TAURI_INTERNALS__.convertFileSrc,delete window.__TAURI_INTERNALS__.metadata,"object"==typeof window.__TAURI_EVENT_PLUGIN_INTERNALS__&&delete window.__TAURI_EVENT_PLUGIN_INTERNALS__.unregisterListener)},mockConvertFileSrc:function(e){J(),window.__TAURI_INTERNALS__.convertFileSrc=function(n,t="asset"){const i=encodeURIComponent(n);return"windows"===e?`http://${t}.localhost/${i}`:`${t}://localhost/${i}`}},mockIPC:function(e,n){function t(e,n){switch(e){case"plugin:event|listen":return function(e){i.has(e.event)||i.set(e.event,[]);return i.get(e.event).push(e.handler),e.handler}(n);case"plugin:event|emit":return function(e){const n=i.get(e.event)||[];for(const t of n)a(t,e);return null}(n);case"plugin:event|unlisten":return function(e){const n=i.get(e.event);if(n){const t=n.indexOf(e.id);-1!==t&&n.splice(t,1)}}(n)}}J();const i=new Map,r=new Map;function s(e){r.delete(e)}function a(e,n){const t=r.get(e);t?t(n):console.warn(`[TAURI] Couldn't find callback id ${e}. This might happen when the app is reloaded while Rust is running an asynchronous operation.`)}window.__TAURI_INTERNALS__.invoke=async function(i,r,s){return(null==n?void 0:n.shouldMockEvents)&&function(e){return e.startsWith("plugin:event|")}(i)?t(i,r):e(i,r)},window.__TAURI_INTERNALS__.transformCallback=function(e,n=!1){const t=window.crypto.getRandomValues(new Uint32Array(1))[0];return r.set(t,i=>(n&&s(t),e&&e(i))),t},window.__TAURI_INTERNALS__.unregisterCallback=s,window.__TAURI_INTERNALS__.runCallback=a,window.__TAURI_INTERNALS__.callbacks=r,window.__TAURI_EVENT_PLUGIN_INTERNALS__.unregisterListener=function(e,n){s(n)}},mockWindows:function(e,...n){J(),window.__TAURI_INTERNALS__.metadata={currentWindow:{label:e},currentWebview:{windowLabel:e,label:e}}}});!function(e){e[e.Audio=1]="Audio",e[e.Cache=2]="Cache",e[e.Config=3]="Config",e[e.Data=4]="Data",e[e.LocalData=5]="LocalData",e[e.Document=6]="Document",e[e.Download=7]="Download",e[e.Picture=8]="Picture",e[e.Public=9]="Public",e[e.Video=10]="Video",e[e.Resource=11]="Resource",e[e.Temp=12]="Temp",e[e.AppConfig=13]="AppConfig",e[e.AppData=14]="AppData",e[e.AppLocalData=15]="AppLocalData",e[e.AppCache=16]="AppCache",e[e.AppLog=17]="AppLog",e[e.Desktop=18]="Desktop",e[e.Executable=19]="Executable",e[e.Font=20]="Font",e[e.Home=21]="Home",e[e.Runtime=22]="Runtime",e[e.Template=23]="Template"}(Q||(Q={}));var K=Object.freeze({__proto__:null,get BaseDirectory(){return Q},appCacheDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.AppCache})},appConfigDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.AppConfig})},appDataDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.AppData})},appLocalDataDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.AppLocalData})},appLogDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.AppLog})},audioDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Audio})},basename:async function(e,n){return p("plugin:path|basename",{path:e,ext:n})},cacheDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Cache})},configDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Config})},dataDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Data})},delimiter:function(){return window.__TAURI_INTERNALS__.plugins.path.delimiter},desktopDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Desktop})},dirname:async function(e){return p("plugin:path|dirname",{path:e})},documentDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Document})},downloadDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Download})},executableDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Executable})},extname:async function(e){return p("plugin:path|extname",{path:e})},fontDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Font})},homeDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Home})},isAbsolute:async function(e){return p("plugin:path|is_absolute",{path:e})},join:async function(...e){return p("plugin:path|join",{paths:e})},localDataDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.LocalData})},normalize:async function(e){return p("plugin:path|normalize",{path:e})},pictureDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Picture})},publicDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Public})},resolve:async function(...e){return p("plugin:path|resolve",{paths:e})},resolveResource:async function(e){return p("plugin:path|resolve_directory",{directory:Q.Resource,path:e})},resourceDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Resource})},runtimeDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Runtime})},sep:function(){return window.__TAURI_INTERNALS__.plugins.path.sep},tempDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Temp})},templateDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Template})},videoDir:async function(){return p("plugin:path|resolve_directory",{directory:Q.Video})}});class Y extends h{constructor(e,n){super(e),this.id=n}static async getById(e){return p("plugin:tray|get_by_id",{id:e}).then(n=>n?new Y(n,e):null)}static async removeById(e){return p("plugin:tray|remove_by_id",{id:e})}static async new(e){(null==e?void 0:e.menu)&&(e.menu=[e.menu.rid,e.menu.kind]),(null==e?void 0:e.icon)&&(e.icon=y(e.icon));const n=new c;if(null==e?void 0:e.action){const t=e.action;n.onmessage=e=>t(function(e){const n=e;return n.position=new T(e.position),n.rect.position=new T(e.rect.position),n.rect.size=new f(e.rect.size),n}(e)),delete e.action}return p("plugin:tray|new",{options:null!=e?e:{},handler:n}).then(([e,n])=>new Y(e,n))}async setIcon(e){let n=null;return e&&(n=y(e)),p("plugin:tray|set_icon",{rid:this.rid,icon:n})}async setMenu(e){return e&&(e=[e.rid,e.kind]),p("plugin:tray|set_menu",{rid:this.rid,menu:e})}async setTooltip(e){return p("plugin:tray|set_tooltip",{rid:this.rid,tooltip:e})}async setTitle(e){return p("plugin:tray|set_title",{rid:this.rid,title:e})}async setVisible(e){return p("plugin:tray|set_visible",{rid:this.rid,visible:e})}async setTempDirPath(e){return p("plugin:tray|set_temp_dir_path",{rid:this.rid,path:e})}async setIconAsTemplate(e){return p("plugin:tray|set_icon_as_template",{rid:this.rid,asTemplate:e})}async setMenuOnLeftClick(e){return p("plugin:tray|set_show_menu_on_left_click",{rid:this.rid,onLeft:e})}async setShowMenuOnLeftClick(e){return p("plugin:tray|set_show_menu_on_left_click",{rid:this.rid,onLeft:e})}}var X,ee,ne=Object.freeze({__proto__:null,TrayIcon:Y});!function(e){e[e.Critical=1]="Critical",e[e.Informational=2]="Informational"}(X||(X={}));class te{constructor(e){this._preventDefault=!1,this.event=e.event,this.id=e.id}preventDefault(){this._preventDefault=!0}isPreventDefault(){return this._preventDefault}}function ie(){return new ae(window.__TAURI_INTERNALS__.metadata.currentWindow.label,{skip:!0})}async function re(){return p("plugin:window|get_all_windows").then(e=>e.map(e=>new ae(e,{skip:!0})))}!function(e){e.None="none",e.Normal="normal",e.Indeterminate="indeterminate",e.Paused="paused",e.Error="error"}(ee||(ee={}));const se=["tauri://created","tauri://error"];class ae{constructor(e,n={}){var t;this.label=e,this.listeners=Object.create(null),(null==n?void 0:n.skip)||p("plugin:window|create",{options:{...n,parent:"string"==typeof n.parent?n.parent:null===(t=n.parent)||void 0===t?void 0:t.label,label:e}}).then(async()=>this.emit("tauri://created")).catch(async e=>this.emit("tauri://error",e))}static async getByLabel(e){var n;return null!==(n=(await re()).find(n=>n.label===e))&&void 0!==n?n:null}static getCurrent(){return ie()}static async getAll(){return re()}static async getFocusedWindow(){for(const e of await re())if(await e.isFocused())return e;return null}async listen(e,n){return this._handleTauriEvent(e,n)?()=>{const t=this.listeners[e];t.splice(t.indexOf(n),1)}:S(e,n,{target:{kind:"Window",label:this.label}})}async once(e,n){return this._handleTauriEvent(e,n)?()=>{const t=this.listeners[e];t.splice(t.indexOf(n),1)}:N(e,n,{target:{kind:"Window",label:this.label}})}async emit(e,n){if(!se.includes(e))return L(e,n);for(const t of this.listeners[e]||[])t({event:e,id:-1,payload:n})}async emitTo(e,n,t){if(!se.includes(n))return C(e,n,t);for(const e of this.listeners[n]||[])e({event:n,id:-1,payload:t})}_handleTauriEvent(e,n){return!!se.includes(e)&&(e in this.listeners?this.listeners[e].push(n):this.listeners[e]=[n],!0)}async scaleFactor(){return p("plugin:window|scale_factor",{label:this.label})}async innerPosition(){return p("plugin:window|inner_position",{label:this.label}).then(e=>new T(e))}async outerPosition(){return p("plugin:window|outer_position",{label:this.label}).then(e=>new T(e))}async innerSize(){return p("plugin:window|inner_size",{label:this.label}).then(e=>new f(e))}async outerSize(){return p("plugin:window|outer_size",{label:this.label}).then(e=>new f(e))}async isFullscreen(){return p("plugin:window|is_fullscreen",{label:this.label})}async isMinimized(){return p("plugin:window|is_minimized",{label:this.label})}async isMaximized(){return p("plugin:window|is_maximized",{label:this.label})}async isFocused(){return p("plugin:window|is_focused",{label:this.label})}async isDecorated(){return p("plugin:window|is_decorated",{label:this.label})}async isResizable(){return p("plugin:window|is_resizable",{label:this.label})}async isMaximizable(){return p("plugin:window|is_maximizable",{label:this.label})}async isMinimizable(){return p("plugin:window|is_minimizable",{label:this.label})}async isClosable(){return p("plugin:window|is_closable",{label:this.label})}async isVisible(){return p("plugin:window|is_visible",{label:this.label})}async title(){return p("plugin:window|title",{label:this.label})}async theme(){return p("plugin:window|theme",{label:this.label})}async isAlwaysOnTop(){return p("plugin:window|is_always_on_top",{label:this.label})}async center(){return p("plugin:window|center",{label:this.label})}async requestUserAttention(e){let n=null;return e&&(n=e===X.Critical?{type:"Critical"}:{type:"Informational"}),p("plugin:window|request_user_attention",{label:this.label,value:n})}async setResizable(e){return p("plugin:window|set_resizable",{label:this.label,value:e})}async setEnabled(e){return p("plugin:window|set_enabled",{label:this.label,value:e})}async isEnabled(){return p("plugin:window|is_enabled",{label:this.label})}async setMaximizable(e){return p("plugin:window|set_maximizable",{label:this.label,value:e})}async setMinimizable(e){return p("plugin:window|set_minimizable",{label:this.label,value:e})}async setClosable(e){return p("plugin:window|set_closable",{label:this.label,value:e})}async setTitle(e){return p("plugin:window|set_title",{label:this.label,value:e})}async maximize(){return p("plugin:window|maximize",{label:this.label})}async unmaximize(){return p("plugin:window|unmaximize",{label:this.label})}async toggleMaximize(){return p("plugin:window|toggle_maximize",{label:this.label})}async minimize(){return p("plugin:window|minimize",{label:this.label})}async unminimize(){return p("plugin:window|unminimize",{label:this.label})}async show(){return p("plugin:window|show",{label:this.label})}async hide(){return p("plugin:window|hide",{label:this.label})}async close(){return p("plugin:window|close",{label:this.label})}async destroy(){return p("plugin:window|destroy",{label:this.label})}async setDecorations(e){return p("plugin:window|set_decorations",{label:this.label,value:e})}async setShadow(e){return p("plugin:window|set_shadow",{label:this.label,value:e})}async setEffects(e){return p("plugin:window|set_effects",{label:this.label,value:e})}async clearEffects(){return p("plugin:window|set_effects",{label:this.label,value:null})}async setAlwaysOnTop(e){return p("plugin:window|set_always_on_top",{label:this.label,value:e})}async setAlwaysOnBottom(e){return p("plugin:window|set_always_on_bottom",{label:this.label,value:e})}async setContentProtected(e){return p("plugin:window|set_content_protected",{label:this.label,value:e})}async setSize(e){return p("plugin:window|set_size",{label:this.label,value:e instanceof k?e:new k(e)})}async setMinSize(e){return p("plugin:window|set_min_size",{label:this.label,value:e instanceof k?e:e?new k(e):null})}async setMaxSize(e){return p("plugin:window|set_max_size",{label:this.label,value:e instanceof k?e:e?new k(e):null})}async setSizeConstraints(e){function n(e){return e?{Logical:e}:null}return p("plugin:window|set_size_constraints",{label:this.label,value:{minWidth:n(null==e?void 0:e.minWidth),minHeight:n(null==e?void 0:e.minHeight),maxWidth:n(null==e?void 0:e.maxWidth),maxHeight:n(null==e?void 0:e.maxHeight)}})}async setPosition(e){return p("plugin:window|set_position",{label:this.label,value:e instanceof I?e:new I(e)})}async setFullscreen(e){return p("plugin:window|set_fullscreen",{label:this.label,value:e})}async setFocus(){return p("plugin:window|set_focus",{label:this.label})}async setIcon(e){return p("plugin:window|set_icon",{label:this.label,value:y(e)})}async setSkipTaskbar(e){return p("plugin:window|set_skip_taskbar",{label:this.label,value:e})}async setCursorGrab(e){return p("plugin:window|set_cursor_grab",{label:this.label,value:e})}async setCursorVisible(e){return p("plugin:window|set_cursor_visible",{label:this.label,value:e})}async setCursorIcon(e){return p("plugin:window|set_cursor_icon",{label:this.label,value:e})}async setBackgroundColor(e){return p("plugin:window|set_background_color",{color:e})}async setCursorPosition(e){return p("plugin:window|set_cursor_position",{label:this.label,value:e instanceof I?e:new I(e)})}async setIgnoreCursorEvents(e){return p("plugin:window|set_ignore_cursor_events",{label:this.label,value:e})}async startDragging(){return p("plugin:window|start_dragging",{label:this.label})}async startResizeDragging(e){return p("plugin:window|start_resize_dragging",{label:this.label,value:e})}async setBadgeCount(e){return p("plugin:window|set_badge_count",{label:this.label,value:e})}async setBadgeLabel(e){return p("plugin:window|set_badge_label",{label:this.label,value:e})}async setOverlayIcon(e){return p("plugin:window|set_overlay_icon",{label:this.label,value:e?y(e):void 0})}async setProgressBar(e){return p("plugin:window|set_progress_bar",{label:this.label,value:e})}async setVisibleOnAllWorkspaces(e){return p("plugin:window|set_visible_on_all_workspaces",{label:this.label,value:e})}async setTitleBarStyle(e){return p("plugin:window|set_title_bar_style",{label:this.label,value:e})}async setTheme(e){return p("plugin:window|set_theme",{label:this.label,value:e})}async onResized(e){return this.listen(E.WINDOW_RESIZED,n=>{n.payload=new f(n.payload),e(n)})}async onMoved(e){return this.listen(E.WINDOW_MOVED,n=>{n.payload=new T(n.payload),e(n)})}async onCloseRequested(e){return this.listen(E.WINDOW_CLOSE_REQUESTED,async n=>{const t=new te(n);await e(t),t.isPreventDefault()||await this.destroy()})}async onDragDropEvent(e){const n=await this.listen(E.DRAG_ENTER,n=>{e({...n,payload:{type:"enter",paths:n.payload.paths,position:new T(n.payload.position)}})}),t=await this.listen(E.DRAG_OVER,n=>{e({...n,payload:{type:"over",position:new T(n.payload.position)}})}),i=await this.listen(E.DRAG_DROP,n=>{e({...n,payload:{type:"drop",paths:n.payload.paths,position:new T(n.payload.position)}})}),r=await this.listen(E.DRAG_LEAVE,n=>{e({...n,payload:{type:"leave"}})});return()=>{n(),i(),t(),r()}}async onFocusChanged(e){const n=await this.listen(E.WINDOW_FOCUS,n=>{e({...n,payload:!0})}),t=await this.listen(E.WINDOW_BLUR,n=>{e({...n,payload:!1})});return()=>{n(),t()}}async onScaleChanged(e){return this.listen(E.WINDOW_SCALE_FACTOR_CHANGED,e)}async onThemeChanged(e){return this.listen(E.WINDOW_THEME_CHANGED,e)}}var le,oe,ue;function ce(e){return null===e?null:{name:e.name,scaleFactor:e.scaleFactor,position:new T(e.position),size:new f(e.size),workArea:{position:new T(e.workArea.position),size:new f(e.workArea.size)}}}!function(e){e.Disabled="disabled",e.Throttle="throttle",e.Suspend="suspend"}(le||(le={})),function(e){e.AppearanceBased="appearanceBased",e.Light="light",e.Dark="dark",e.MediumLight="mediumLight",e.UltraDark="ultraDark",e.Titlebar="titlebar",e.Selection="selection",e.Menu="menu",e.Popover="popover",e.Sidebar="sidebar",e.HeaderView="headerView",e.Sheet="sheet",e.WindowBackground="windowBackground",e.HudWindow="hudWindow",e.FullScreenUI="fullScreenUI",e.Tooltip="tooltip",e.ContentBackground="contentBackground",e.UnderWindowBackground="underWindowBackground",e.UnderPageBackground="underPageBackground",e.Mica="mica",e.Blur="blur",e.Acrylic="acrylic",e.Tabbed="tabbed",e.TabbedDark="tabbedDark",e.TabbedLight="tabbedLight"}(oe||(oe={})),function(e){e.FollowsWindowActiveState="followsWindowActiveState",e.Active="active",e.Inactive="inactive"}(ue||(ue={}));var de=Object.freeze({__proto__:null,CloseRequestedEvent:te,get Effect(){return oe},get EffectState(){return ue},LogicalPosition:A,LogicalSize:v,PhysicalPosition:T,PhysicalSize:f,get ProgressBarStatus(){return ee},get UserAttentionType(){return X},Window:ae,availableMonitors:async function(){return p("plugin:window|available_monitors").then(e=>e.map(ce))},currentMonitor:async function(){return p("plugin:window|current_monitor").then(ce)},cursorPosition:async function(){return p("plugin:window|cursor_position").then(e=>new T(e))},getAllWindows:re,getCurrentWindow:ie,monitorFromPoint:async function(e,n){return p("plugin:window|monitor_from_point",{x:e,y:n}).then(ce)},primaryMonitor:async function(){return p("plugin:window|primary_monitor").then(ce)}});function pe(){return new _e(ie(),window.__TAURI_INTERNALS__.metadata.currentWebview.label,{skip:!0})}async function he(){return p("plugin:webview|get_all_webviews").then(e=>e.map(e=>new _e(new ae(e.windowLabel,{skip:!0}),e.label,{skip:!0})))}const we=["tauri://created","tauri://error"];class _e{constructor(e,n,t){this.window=e,this.label=n,this.listeners=Object.create(null),(null==t?void 0:t.skip)||p("plugin:webview|create_webview",{windowLabel:e.label,options:{...t,label:n}}).then(async()=>this.emit("tauri://created")).catch(async e=>this.emit("tauri://error",e))}static async getByLabel(e){var n;return null!==(n=(await he()).find(n=>n.label===e))&&void 0!==n?n:null}static getCurrent(){return pe()}static async getAll(){return he()}async listen(e,n){return this._handleTauriEvent(e,n)?()=>{const t=this.listeners[e];t.splice(t.indexOf(n),1)}:S(e,n,{target:{kind:"Webview",label:this.label}})}async once(e,n){return this._handleTauriEvent(e,n)?()=>{const t=this.listeners[e];t.splice(t.indexOf(n),1)}:N(e,n,{target:{kind:"Webview",label:this.label}})}async emit(e,n){if(!we.includes(e))return L(e,n);for(const t of this.listeners[e]||[])t({event:e,id:-1,payload:n})}async emitTo(e,n,t){if(!we.includes(n))return C(e,n,t);for(const e of this.listeners[n]||[])e({event:n,id:-1,payload:t})}_handleTauriEvent(e,n){return!!we.includes(e)&&(e in this.listeners?this.listeners[e].push(n):this.listeners[e]=[n],!0)}async position(){return p("plugin:webview|webview_position",{label:this.label}).then(e=>new T(e))}async size(){return p("plugin:webview|webview_size",{label:this.label}).then(e=>new f(e))}async close(){return p("plugin:webview|webview_close",{label:this.label})}async setSize(e){return p("plugin:webview|set_webview_size",{label:this.label,value:e instanceof k?e:new k(e)})}async setPosition(e){return p("plugin:webview|set_webview_position",{label:this.label,value:e instanceof I?e:new I(e)})}async setFocus(){return p("plugin:webview|set_webview_focus",{label:this.label})}async setAutoResize(e){return p("plugin:webview|set_webview_auto_resize",{label:this.label,value:e})}async hide(){return p("plugin:webview|webview_hide",{label:this.label})}async show(){return p("plugin:webview|webview_show",{label:this.label})}async setZoom(e){return p("plugin:webview|set_webview_zoom",{label:this.label,value:e})}async reparent(e){return p("plugin:webview|reparent",{label:this.label,window:"string"==typeof e?e:e.label})}async clearAllBrowsingData(){return p("plugin:webview|clear_all_browsing_data")}async setBackgroundColor(e){return p("plugin:webview|set_webview_background_color",{color:e})}async onDragDropEvent(e){const n=await this.listen(E.DRAG_ENTER,n=>{e({...n,payload:{type:"enter",paths:n.payload.paths,position:new T(n.payload.position)}})}),t=await this.listen(E.DRAG_OVER,n=>{e({...n,payload:{type:"over",position:new T(n.payload.position)}})}),i=await this.listen(E.DRAG_DROP,n=>{e({...n,payload:{type:"drop",paths:n.payload.paths,position:new T(n.payload.position)}})}),r=await this.listen(E.DRAG_LEAVE,n=>{e({...n,payload:{type:"leave"}})});return()=>{n(),i(),t(),r()}}}var ye,ge,be=Object.freeze({__proto__:null,Webview:_e,getAllWebviews:he,getCurrentWebview:pe});function me(){const e=pe();return new fe(e.label,{skip:!0})}async function ve(){return p("plugin:window|get_all_windows").then(e=>e.map(e=>new fe(e,{skip:!0})))}class fe{constructor(e,n={}){var t;this.label=e,this.listeners=Object.create(null),(null==n?void 0:n.skip)||p("plugin:webview|create_webview_window",{options:{...n,parent:"string"==typeof n.parent?n.parent:null===(t=n.parent)||void 0===t?void 0:t.label,label:e}}).then(async()=>this.emit("tauri://created")).catch(async e=>this.emit("tauri://error",e))}static async getByLabel(e){var n;const t=null!==(n=(await ve()).find(n=>n.label===e))&&void 0!==n?n:null;return t?new fe(t.label,{skip:!0}):null}static getCurrent(){return me()}static async getAll(){return ve()}async listen(e,n){return this._handleTauriEvent(e,n)?()=>{const t=this.listeners[e];t.splice(t.indexOf(n),1)}:S(e,n,{target:{kind:"WebviewWindow",label:this.label}})}async once(e,n){return this._handleTauriEvent(e,n)?()=>{const t=this.listeners[e];t.splice(t.indexOf(n),1)}:N(e,n,{target:{kind:"WebviewWindow",label:this.label}})}async setBackgroundColor(e){return p("plugin:window|set_background_color",{color:e}).then(()=>p("plugin:webview|set_webview_background_color",{color:e}))}}ye=fe,ge=[ae,_e],(Array.isArray(ge)?ge:[ge]).forEach(e=>{Object.getOwnPropertyNames(e.prototype).forEach(n=>{var t;"object"==typeof ye.prototype&&ye.prototype&&n in ye.prototype||Object.defineProperty(ye.prototype,n,null!==(t=Object.getOwnPropertyDescriptor(e.prototype,n))&&void 0!==t?t:Object.create(null))})});var ke=Object.freeze({__proto__:null,WebviewWindow:fe,getAllWebviewWindows:ve,getCurrentWebviewWindow:me});return e.app=m,e.core=w,e.dpi=R,e.event=W,e.image=b,e.menu=q,e.mocks=Z,e.path=K,e.tray=ne,e.webview=be,e.webviewWindow=ke,e.window=de,e}({});window.__TAURI__=__TAURI_IIFE__;
