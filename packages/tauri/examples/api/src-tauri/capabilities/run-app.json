{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "run-app", "description": "permissions to run the app", "windows": ["main", "main-*"], "permissions": ["core:window:allow-is-enabled", "core:window:allow-set-enabled", {"identifier": "allow-log-operation", "allow": [{"event": "tauri-click"}]}, "allow-perform-request", "allow-echo", "allow-spam", "app-menu:default", "sample:allow-ping-scoped", "sample:global-scope", "core:default", "core:app:allow-app-hide", "core:app:allow-app-show", "core:app:allow-set-app-theme", "core:app:allow-set-dock-visibility", "core:window:allow-set-theme", "core:window:allow-center", "core:window:allow-request-user-attention", "core:window:allow-set-resizable", "core:window:allow-set-maximizable", "core:window:allow-set-minimizable", "core:window:allow-set-closable", "core:window:allow-set-title", "core:window:allow-maximize", "core:window:allow-unmaximize", "core:window:allow-minimize", "core:window:allow-unminimize", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-close", "core:window:allow-set-decorations", "core:window:allow-set-shadow", "core:window:allow-set-effects", "core:window:allow-set-always-on-top", "core:window:allow-set-always-on-bottom", "core:window:allow-set-content-protected", "core:window:allow-set-size", "core:window:allow-set-min-size", "core:window:allow-set-max-size", "core:window:allow-set-position", "core:window:allow-set-fullscreen", "core:window:allow-set-focus", "core:window:allow-set-skip-taskbar", "core:window:allow-set-cursor-grab", "core:window:allow-set-cursor-visible", "core:window:allow-set-cursor-icon", "core:window:allow-set-cursor-position", "core:window:allow-set-ignore-cursor-events", "core:window:allow-start-dragging", "core:window:allow-set-progress-bar", "core:window:allow-set-icon", "core:window:allow-toggle-maximize", "core:webview:allow-create-webview-window", "core:webview:allow-print"]}