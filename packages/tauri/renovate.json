{"extends": ["config:recommended"], "baseBranches": ["dev"], "labels": ["type: chore"], "enabledManagers": ["cargo", "npm"], "rangeStrategy": "replace", "packageRules": [{"semanticCommitType": "chore", "matchPackageNames": ["*"]}, {"description": "Disable node/pnpm version updates", "matchPackageNames": ["node", "pnpm"], "matchDepTypes": ["engines", "packageManager"], "enabled": false}, {"description": "Disable oxc_* crates because of MSRV and PR spam", "matchPackageNames": ["oxc_*"], "enabled": false}], "postUpdateOptions": ["pnpmDedupe"]}