pluginManagement {
    repositories {
        mavenCentral()
        gradlePluginPortal()
        google()
    }
    resolutionStrategy {
        eachPlugin {
            switch (requested.id.id) {
                case "com.android.library":
                    useVersion("8.0.2")
                    break
                case "org.jetbrains.kotlin.android":
                    useVersion("1.8.20")
                    break
            }
        }
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        mavenCentral()
        google()

    }
}

include ':tauri-android'
project(':tauri-android').projectDir = new File('./.tauri/tauri-api')
