---
source: crates/tauri-cli/src/helpers/pbxproj.rs
expression: "super::parse(fixtures_path.join(\"project.pbxproj\")).expect(\"failed to parse pbxproj\")"
---
Pbxproj {
    xc_build_configuration: {
        "A83F70B4C02DD0222038C7F1": XCBuildConfiguration {
            build_settings: [
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 262,
                    key: "ALWAYS_SEARCH_USER_PATHS",
                    value: "NO",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 263,
                    key: "CLANG_ANALYZER_NONNULL",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 264,
                    key: "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION",
                    value: "YES_AGGRESSIVE",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 265,
                    key: "CLANG_CXX_LANGUAGE_STANDARD",
                    value: "\"gnu++14\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 266,
                    key: "CLANG_CXX_LIBRARY",
                    value: "\"libc++\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 267,
                    key: "CLANG_ENABLE_MODULES",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 268,
                    key: "CLANG_ENABLE_OBJC_ARC",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 269,
                    key: "CLANG_ENABLE_OBJC_WEAK",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 270,
                    key: "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 271,
                    key: "CLANG_WARN_BOOL_CONVERSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 272,
                    key: "CLANG_WARN_COMMA",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 273,
                    key: "CLANG_WARN_CONSTANT_CONVERSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 274,
                    key: "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 275,
                    key: "CLANG_WARN_DIRECT_OBJC_ISA_USAGE",
                    value: "YES_ERROR",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 276,
                    key: "CLANG_WARN_DOCUMENTATION_COMMENTS",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 277,
                    key: "CLANG_WARN_EMPTY_BODY",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 278,
                    key: "CLANG_WARN_ENUM_CONVERSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 279,
                    key: "CLANG_WARN_INFINITE_RECURSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 280,
                    key: "CLANG_WARN_INT_CONVERSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 281,
                    key: "CLANG_WARN_NON_LITERAL_NULL_CONVERSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 282,
                    key: "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 283,
                    key: "CLANG_WARN_OBJC_LITERAL_CONVERSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 284,
                    key: "CLANG_WARN_OBJC_ROOT_CLASS",
                    value: "YES_ERROR",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 285,
                    key: "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 286,
                    key: "CLANG_WARN_RANGE_LOOP_ANALYSIS",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 287,
                    key: "CLANG_WARN_STRICT_PROTOTYPES",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 288,
                    key: "CLANG_WARN_SUSPICIOUS_MOVE",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 289,
                    key: "CLANG_WARN_UNGUARDED_AVAILABILITY",
                    value: "YES_AGGRESSIVE",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 290,
                    key: "CLANG_WARN_UNREACHABLE_CODE",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 291,
                    key: "CLANG_WARN__DUPLICATE_METHOD_MATCH",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 292,
                    key: "COPY_PHASE_STRIP",
                    value: "NO",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 293,
                    key: "DEBUG_INFORMATION_FORMAT",
                    value: "\"dwarf-with-dsym\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 294,
                    key: "ENABLE_NS_ASSERTIONS",
                    value: "NO",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 295,
                    key: "ENABLE_STRICT_OBJC_MSGSEND",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 296,
                    key: "GCC_C_LANGUAGE_STANDARD",
                    value: "gnu11",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 297,
                    key: "GCC_NO_COMMON_BLOCKS",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 298,
                    key: "GCC_WARN_64_TO_32_BIT_CONVERSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 299,
                    key: "GCC_WARN_ABOUT_RETURN_TYPE",
                    value: "YES_ERROR",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 300,
                    key: "GCC_WARN_UNDECLARED_SELECTOR",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 301,
                    key: "GCC_WARN_UNINITIALIZED_AUTOS",
                    value: "YES_AGGRESSIVE",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 302,
                    key: "GCC_WARN_UNUSED_FUNCTION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 303,
                    key: "GCC_WARN_UNUSED_VARIABLE",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 304,
                    key: "IPHONEOS_DEPLOYMENT_TARGET",
                    value: "13.0",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 305,
                    key: "MTL_ENABLE_DEBUG_INFO",
                    value: "NO",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 306,
                    key: "MTL_FAST_MATH",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 307,
                    key: "PRODUCT_NAME",
                    value: "\"$(TARGET_NAME)\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 308,
                    key: "SDKROOT",
                    value: "iphoneos",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 309,
                    key: "SWIFT_COMPILATION_MODE",
                    value: "wholemodule",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 310,
                    key: "SWIFT_OPTIMIZATION_LEVEL",
                    value: "\"-O\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 311,
                    key: "SWIFT_VERSION",
                    value: "5.0",
                },
            ],
        },
        "B6AD77E490F315562F75D3D7": XCBuildConfiguration {
            build_settings: [
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 318,
                    key: "ALWAYS_SEARCH_USER_PATHS",
                    value: "NO",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 319,
                    key: "CLANG_ANALYZER_NONNULL",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 320,
                    key: "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION",
                    value: "YES_AGGRESSIVE",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 321,
                    key: "CLANG_CXX_LANGUAGE_STANDARD",
                    value: "\"gnu++14\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 322,
                    key: "CLANG_CXX_LIBRARY",
                    value: "\"libc++\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 323,
                    key: "CLANG_ENABLE_MODULES",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 324,
                    key: "CLANG_ENABLE_OBJC_ARC",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 325,
                    key: "CLANG_ENABLE_OBJC_WEAK",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 326,
                    key: "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 327,
                    key: "CLANG_WARN_BOOL_CONVERSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 328,
                    key: "CLANG_WARN_COMMA",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 329,
                    key: "CLANG_WARN_CONSTANT_CONVERSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 330,
                    key: "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 331,
                    key: "CLANG_WARN_DIRECT_OBJC_ISA_USAGE",
                    value: "YES_ERROR",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 332,
                    key: "CLANG_WARN_DOCUMENTATION_COMMENTS",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 333,
                    key: "CLANG_WARN_EMPTY_BODY",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 334,
                    key: "CLANG_WARN_ENUM_CONVERSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 335,
                    key: "CLANG_WARN_INFINITE_RECURSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 336,
                    key: "CLANG_WARN_INT_CONVERSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 337,
                    key: "CLANG_WARN_NON_LITERAL_NULL_CONVERSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 338,
                    key: "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 339,
                    key: "CLANG_WARN_OBJC_LITERAL_CONVERSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 340,
                    key: "CLANG_WARN_OBJC_ROOT_CLASS",
                    value: "YES_ERROR",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 341,
                    key: "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 342,
                    key: "CLANG_WARN_RANGE_LOOP_ANALYSIS",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 343,
                    key: "CLANG_WARN_STRICT_PROTOTYPES",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 344,
                    key: "CLANG_WARN_SUSPICIOUS_MOVE",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 345,
                    key: "CLANG_WARN_UNGUARDED_AVAILABILITY",
                    value: "YES_AGGRESSIVE",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 346,
                    key: "CLANG_WARN_UNREACHABLE_CODE",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 347,
                    key: "CLANG_WARN__DUPLICATE_METHOD_MATCH",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 348,
                    key: "COPY_PHASE_STRIP",
                    value: "NO",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 349,
                    key: "DEBUG_INFORMATION_FORMAT",
                    value: "dwarf",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 350,
                    key: "ENABLE_STRICT_OBJC_MSGSEND",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 351,
                    key: "ENABLE_TESTABILITY",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 352,
                    key: "GCC_C_LANGUAGE_STANDARD",
                    value: "gnu11",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 353,
                    key: "GCC_DYNAMIC_NO_PIC",
                    value: "NO",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 354,
                    key: "GCC_NO_COMMON_BLOCKS",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 355,
                    key: "GCC_OPTIMIZATION_LEVEL",
                    value: "0",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 356,
                    key: "GCC_PREPROCESSOR_DEFINITIONS",
                    value: "(\t\t\t\t\t\"$(inherited)\",\n\t\t\t\t\t\"DEBUG=1\",\n\t\t\t\t);\n",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 360,
                    key: "GCC_WARN_64_TO_32_BIT_CONVERSION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 361,
                    key: "GCC_WARN_ABOUT_RETURN_TYPE",
                    value: "YES_ERROR",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 362,
                    key: "GCC_WARN_UNDECLARED_SELECTOR",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 363,
                    key: "GCC_WARN_UNINITIALIZED_AUTOS",
                    value: "YES_AGGRESSIVE",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 364,
                    key: "GCC_WARN_UNUSED_FUNCTION",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 365,
                    key: "GCC_WARN_UNUSED_VARIABLE",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 366,
                    key: "IPHONEOS_DEPLOYMENT_TARGET",
                    value: "13.0",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 367,
                    key: "MTL_ENABLE_DEBUG_INFO",
                    value: "INCLUDE_SOURCE",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 368,
                    key: "MTL_FAST_MATH",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 369,
                    key: "ONLY_ACTIVE_ARCH",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 370,
                    key: "PRODUCT_NAME",
                    value: "\"$(TARGET_NAME)\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 371,
                    key: "SDKROOT",
                    value: "iphoneos",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 372,
                    key: "SWIFT_ACTIVE_COMPILATION_CONDITIONS",
                    value: "DEBUG",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 373,
                    key: "SWIFT_OPTIMIZATION_LEVEL",
                    value: "\"-Onone\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 374,
                    key: "SWIFT_VERSION",
                    value: "5.0",
                },
            ],
        },
        "BF284FE6E7AE0C8DDCCE398B": XCBuildConfiguration {
            build_settings: [
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 381,
                    key: "ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 382,
                    key: "ARCHS",
                    value: "(\t\t\t\t\tarm64,\n\t\t\t\t);\n",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 385,
                    key: "ASSETCATALOG_COMPILER_APPICON_NAME",
                    value: "AppIcon",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 386,
                    key: "CODE_SIGN_ENTITLEMENTS",
                    value: "api_iOS/api_iOS.entitlements",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 387,
                    key: "CODE_SIGN_IDENTITY",
                    value: "\"iPhone Developer\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 388,
                    key: "DEVELOPMENT_TEAM",
                    value: "Q93MBH6S2F",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 389,
                    key: "ENABLE_BITCODE",
                    value: "NO",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 390,
                    key: "\"EXCLUDED_ARCHS[sdk=iphoneos*]\"",
                    value: "\"x86_64\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 391,
                    key: "FRAMEWORK_SEARCH_PATHS",
                    value: "(\t\t\t\t\t\"$(inherited)\",\n\t\t\t\t\t\"\\\".\\\"\",\n\t\t\t\t);\n",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 395,
                    key: "INFOPLIST_FILE",
                    value: "api_iOS/Info.plist",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 396,
                    key: "LD_RUNPATH_SEARCH_PATHS",
                    value: "(\t\t\t\t\t\"$(inherited)\",\n\t\t\t\t\t\"@executable_path/Frameworks\",\n\t\t\t\t);\n",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 400,
                    key: "\"LIBRARY_SEARCH_PATHS[arch=arm64]\"",
                    value: "\"$(inherited) $(PROJECT_DIR)/Externals/arm64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 401,
                    key: "\"LIBRARY_SEARCH_PATHS[arch=x86_64]\"",
                    value: "\"$(inherited) $(PROJECT_DIR)/Externals/x86_64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 402,
                    key: "PRODUCT_BUNDLE_IDENTIFIER",
                    value: "com.tauri.api",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 403,
                    key: "PRODUCT_NAME",
                    value: "\"Tauri API\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 404,
                    key: "SDKROOT",
                    value: "iphoneos",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 405,
                    key: "TARGETED_DEVICE_FAMILY",
                    value: "\"1,2\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 406,
                    key: "VALID_ARCHS",
                    value: "\"arm64\"",
                },
            ],
        },
        "DB_0E254D0FD84970B57F6410": XCBuildConfiguration {
            build_settings: [
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 413,
                    key: "ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES",
                    value: "YES",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 414,
                    key: "ARCHS",
                    value: "(\t\t\t\t\tarm64,\n\t\t\t\t);\n",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 417,
                    key: "ASSETCATALOG_COMPILER_APPICON_NAME",
                    value: "AppIcon",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 418,
                    key: "CODE_SIGN_ENTITLEMENTS",
                    value: "api_iOS/api_iOS.entitlements",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 419,
                    key: "CODE_SIGN_IDENTITY",
                    value: "\"iPhone Developer\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 420,
                    key: "DEVELOPMENT_TEAM",
                    value: "Q93MBH6S2F",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 421,
                    key: "ENABLE_BITCODE",
                    value: "NO",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 422,
                    key: "\"EXCLUDED_ARCHS[sdk=iphoneos*]\"",
                    value: "\"x86_64\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 423,
                    key: "FRAMEWORK_SEARCH_PATHS",
                    value: "(\t\t\t\t\t\"$(inherited)\",\n\t\t\t\t\t\"\\\".\\\"\",\n\t\t\t\t);\n",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 427,
                    key: "INFOPLIST_FILE",
                    value: "api_iOS/Info.plist",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 428,
                    key: "LD_RUNPATH_SEARCH_PATHS",
                    value: "(\t\t\t\t\t\"$(inherited)\",\n\t\t\t\t\t\"@executable_path/Frameworks\",\n\t\t\t\t);\n",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 432,
                    key: "\"LIBRARY_SEARCH_PATHS[arch=arm64]\"",
                    value: "\"$(inherited) $(PROJECT_DIR)/Externals/arm64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 433,
                    key: "\"LIBRARY_SEARCH_PATHS[arch=x86_64]\"",
                    value: "\"$(inherited) $(PROJECT_DIR)/Externals/x86_64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 434,
                    key: "PRODUCT_BUNDLE_IDENTIFIER",
                    value: "com.tauri.api",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 435,
                    key: "PRODUCT_NAME",
                    value: "\"Tauri API\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 436,
                    key: "SDKROOT",
                    value: "iphoneos",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 437,
                    key: "TARGETED_DEVICE_FAMILY",
                    value: "\"1,2\"",
                },
                BuildSettings {
                    identation: "\t\t\t\t",
                    line_number: 438,
                    key: "VALID_ARCHS",
                    value: "\"arm64\"",
                },
            ],
        },
    },
    xc_configuration_list: {
        "01CBC40275452376830D79B1": XCConfigurationList {
            comment: "/* Build configuration list for PBXNativeTarget \"api_iOS\" */",
            build_configurations: [
                BuildConfigurationRef {
                    id: "BF284FE6E7AE0C8DDCCE398B",
                    comments: "/* debug */",
                },
                BuildConfigurationRef {
                    id: "DB_0E254D0FD84970B57F6410",
                    comments: "/* release */",
                },
            ],
        },
        "8FA67D0F928A09CD639137D1": XCConfigurationList {
            comment: "/* Build configuration list for PBXProject \"api\" */",
            build_configurations: [
                BuildConfigurationRef {
                    id: "B6AD77E490F315562F75D3D7",
                    comments: "/* debug */",
                },
                BuildConfigurationRef {
                    id: "A83F70B4C02DD0222038C7F1",
                    comments: "/* release */",
                },
            ],
        },
    },
}
