{"$schema": "http://json-schema.org/draft-07/schema#", "title": "<PERSON><PERSON><PERSON>", "description": "An argument for fine grained behavior control of Tauri commands.\n\n It can be of any serde serializable type and is used to allow or prevent certain actions inside a Tauri command.\n The configured scope is passed to the command and will be enforced by the command implementation.\n\n ## Example\n\n ```json\n {\n   \"allow\": [{ \"path\": \"$HOME/**\" }],\n   \"deny\": [{ \"path\": \"$HOME/secret.txt\" }]\n }\n ```", "type": "object", "properties": {"allow": {"description": "Data that defines what is allowed by the scope.", "type": ["array", "null"], "items": {"$ref": "#/definitions/Value"}}, "deny": {"description": "Data that defines what is denied by the scope. This should be prioritized by validation logic.", "type": ["array", "null"], "items": {"$ref": "#/definitions/Value"}}}, "definitions": {"Value": {"description": "All supported ACL values.", "anyOf": [{"description": "Represents a null JSON value.", "type": "null"}, {"description": "Represents a [`bool`].", "type": "boolean"}, {"description": "Represents a valid ACL [`Number`].", "allOf": [{"$ref": "#/definitions/Number"}]}, {"description": "Represents a [`String`].", "type": "string"}, {"description": "Represents a list of other [`Value`]s.", "type": "array", "items": {"$ref": "#/definitions/Value"}}, {"description": "Represents a map of [`String`] keys to [`Value`]s.", "type": "object", "additionalProperties": {"$ref": "#/definitions/Value"}}]}, "Number": {"description": "A valid ACL number.", "anyOf": [{"description": "Represents an [`i64`].", "type": "integer", "format": "int64"}, {"description": "Represents a [`f64`].", "type": "number", "format": "double"}]}}}