## Default Permission

Default permissions for the plugin.

#### This default permission set includes the following:

- `allow-version`
- `allow-name`
- `allow-tauri-version`
- `allow-identifier`
- `allow-bundle-type`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`core:app:allow-app-hide`

</td>
<td>

Enables the app_hide command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:deny-app-hide`

</td>
<td>

Denies the app_hide command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:allow-app-show`

</td>
<td>

Enables the app_show command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:deny-app-show`

</td>
<td>

Denies the app_show command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:allow-bundle-type`

</td>
<td>

Enables the bundle_type command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:deny-bundle-type`

</td>
<td>

Denies the bundle_type command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:allow-default-window-icon`

</td>
<td>

Enables the default_window_icon command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:deny-default-window-icon`

</td>
<td>

Denies the default_window_icon command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:allow-fetch-data-store-identifiers`

</td>
<td>

Enables the fetch_data_store_identifiers command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:deny-fetch-data-store-identifiers`

</td>
<td>

Denies the fetch_data_store_identifiers command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:allow-identifier`

</td>
<td>

Enables the identifier command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:deny-identifier`

</td>
<td>

Denies the identifier command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:allow-name`

</td>
<td>

Enables the name command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:deny-name`

</td>
<td>

Denies the name command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:allow-remove-data-store`

</td>
<td>

Enables the remove_data_store command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:deny-remove-data-store`

</td>
<td>

Denies the remove_data_store command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:allow-set-app-theme`

</td>
<td>

Enables the set_app_theme command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:deny-set-app-theme`

</td>
<td>

Denies the set_app_theme command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:allow-set-dock-visibility`

</td>
<td>

Enables the set_dock_visibility command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:deny-set-dock-visibility`

</td>
<td>

Denies the set_dock_visibility command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:allow-tauri-version`

</td>
<td>

Enables the tauri_version command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:deny-tauri-version`

</td>
<td>

Denies the tauri_version command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:allow-version`

</td>
<td>

Enables the version command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:app:deny-version`

</td>
<td>

Denies the version command without any pre-configured scope.

</td>
</tr>
</table>
