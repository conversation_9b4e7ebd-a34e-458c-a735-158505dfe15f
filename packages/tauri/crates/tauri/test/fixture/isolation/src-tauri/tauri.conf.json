{"$schema": "../../../../../../crates/tauri-schema-generator/schemas/config.schema.json", "identifier": "isolation.tauri.example", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:4000"}, "app": {"windows": [{"title": "Isolation Tauri App"}], "security": {"csp": "default-src blob: data: filesystem: ws: wss: http: https: tauri: 'unsafe-eval' 'unsafe-inline' 'self'; connect-src ipc: http://ipc.localhost", "pattern": {"use": "isolation", "options": {"dir": "../isolation-dist"}}}}, "bundle": {"active": true}}