## Default Permission

Default permissions for the plugin, which enables all commands.

#### This default permission set includes the following:

- `allow-close`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`core:resources:allow-close`

</td>
<td>

Enables the close command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:resources:deny-close`

</td>
<td>

Denies the close command without any pre-configured scope.

</td>
</tr>
</table>
