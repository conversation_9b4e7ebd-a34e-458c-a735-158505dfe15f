[package]
name = "tauri-app"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
license = ""
repository = ""
edition = "2021"
rust-version = "1.77.2"

[lib]
name = "tauri_app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = {{ tauri_build_dep }}

[dependencies]
tauri = {{ tauri_example_dep }}
tauri-plugin-{{ plugin_name }} = { path = "../../../" }
