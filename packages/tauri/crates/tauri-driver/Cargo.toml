[package]
name = "tauri-driver"
version = "2.0.4"
authors = ["Tauri Programme within The Commons Conservancy"]
categories = ["gui", "web-programming"]
license = "Apache-2.0 OR MIT"
homepage = "https://tauri.app"
repository = "https://github.com/tauri-apps/tauri"
description = "Webdriver server for Tauri applications"
readme = "README.md"
edition = "2021"
rust-version = "1.77.2"

[dependencies]
anyhow = "1"
futures = "0.3"
futures-util = "0.3"
http-body-util = "0.1"
hyper = { version = "1", features = ["client", "http1", "server"] }
hyper-util = { version = "0.1", features = [
  "client",
  "client-legacy",
  "http1",
  "server",
  "tokio",
] }
pico-args = "0.5"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tokio = { version = "1", features = ["macros"] }
which = "7"

[target."cfg(unix)".dependencies]
signal-hook = "0.3"
signal-hook-tokio = { version = "0.3", features = ["futures-v0_3"] }

[target."cfg(windows)".dependencies]
win32job = "2"
