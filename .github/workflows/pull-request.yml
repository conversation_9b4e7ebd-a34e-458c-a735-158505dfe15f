name: PR checks
on:
  pull_request:
    branches: [main]
permissions:
  contents: write
  pull-requests: write
jobs:
  rust_lint:
    runs-on: ubuntu-latest
    env:
      RUSTFLAGS: '-C target-cpu=skylake'
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: 'true'
      - name: Install minimal stable with clippy and rustfmt
        uses: actions-rust-lang/setup-rust-toolchain@v1
        with:
          toolchain: stable
          override: true
      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y pkg-config libfontconfig-dev libglib2.0-dev libgtk-3-dev libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev libsoup-3.0-dev
      - name: Format
        working-directory: apps/readest-app/src-tauri
        run: cargo fmt --check
      - name: Clippy Check
        working-directory: apps/readest-app/src-tauri
        run: cargo clippy -p Readest --no-deps -- -D warnings

  build_web_app:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        config:
          - platform: 'web'
          - platform: 'tauri'
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: 'true'

      - name: setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.14.0

      - name: setup node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: pnpm

      - name: cache Next.js build
        uses: actions/cache@v4
        with:
          path: apps/readest-app/.next/cache
          key: nextjs-${{ runner.os }}-${{ hashFiles('apps/readest-app/package.json', 'pnpm-lock.yaml') }}
          restore-keys: nextjs-${{ runner.os }}-

      - name: install Dependencies
        working-directory: apps/readest-app
        run: |
          pnpm install && pnpm setup-pdfjs

      - name: run tests
        working-directory: apps/readest-app
        run: |
          pnpm test -- --watch=false

      - name: build the web App
        if: matrix.config.platform == 'web'
        working-directory: apps/readest-app
        run: |
          pnpm build-web && pnpm check:all

      - name: build the Tauri App
        if: matrix.config.platform == 'tauri'
        working-directory: apps/readest-app
        run: |
          pnpm build && pnpm check:all
