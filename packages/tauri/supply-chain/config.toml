
# cargo-vet config file

[cargo-vet]
version = "0.9"

[imports.bytecode-alliance]
url = "https://raw.githubusercontent.com/bytecodealliance/wasmtime/main/supply-chain/audits.toml"

[imports.embark-studios]
url = "https://raw.githubusercontent.com/EmbarkStudios/rust-ecosystem/main/audits.toml"

[imports.google]
url = "https://raw.githubusercontent.com/google/supply-chain/main/audits.toml"

[imports.isrg]
url = "https://raw.githubusercontent.com/divviup/libprio-rs/main/supply-chain/audits.toml"

[imports.mozilla]
url = "https://raw.githubusercontent.com/mozilla/supply-chain/main/audits.toml"

[imports.zcash]
url = "https://raw.githubusercontent.com/zcash/rust-ecosystem/main/supply-chain/audits.toml"

[policy.tauri]
audit-as-crates-io = true

[policy.tauri-build]
audit-as-crates-io = true

[policy.tauri-codegen]
audit-as-crates-io = true

[policy.tauri-macros]
audit-as-crates-io = true

[policy.tauri-plugin]
audit-as-crates-io = true

[policy.tauri-runtime]
audit-as-crates-io = true

[policy.tauri-runtime-wry]
audit-as-crates-io = true

[policy.tauri-utils]
audit-as-crates-io = true

[[exemptions.addr2line]]
version = "0.21.0"
criteria = "safe-to-deploy"

[[exemptions.alloc-no-stdlib]]
version = "2.0.4"
criteria = "safe-to-deploy"

[[exemptions.alloc-stdlib]]
version = "0.2.2"
criteria = "safe-to-deploy"

[[exemptions.android-tzdata]]
version = "0.1.1"
criteria = "safe-to-deploy"

[[exemptions.as-raw-xcb-connection]]
version = "1.0.1"
criteria = "safe-to-deploy"

[[exemptions.atk]]
version = "0.18.0"
criteria = "safe-to-deploy"

[[exemptions.atk-sys]]
version = "0.18.0"
criteria = "safe-to-deploy"

[[exemptions.base64]]
version = "0.21.7"
criteria = "safe-to-deploy"

[[exemptions.bitflags]]
version = "1.3.2"
criteria = "safe-to-deploy"

[[exemptions.block]]
version = "0.1.6"
criteria = "safe-to-deploy"

[[exemptions.brotli]]
version = "3.4.0"
criteria = "safe-to-deploy"

[[exemptions.brotli-decompressor]]
version = "2.5.1"
criteria = "safe-to-deploy"

[[exemptions.bytemuck]]
version = "1.14.3"
criteria = "safe-to-deploy"

[[exemptions.bytemuck_derive]]
version = "1.5.0"
criteria = "safe-to-deploy"

[[exemptions.cairo-rs]]
version = "0.18.5"
criteria = "safe-to-deploy"

[[exemptions.cairo-sys-rs]]
version = "0.18.2"
criteria = "safe-to-deploy"

[[exemptions.camino]]
version = "1.1.6"
criteria = "safe-to-deploy"

[[exemptions.cargo-platform]]
version = "0.1.7"
criteria = "safe-to-deploy"

[[exemptions.cargo_toml]]
version = "0.17.2"
criteria = "safe-to-deploy"

[[exemptions.cesu8]]
version = "1.1.0"
criteria = "safe-to-deploy"

[[exemptions.cfb]]
version = "0.7.3"
criteria = "safe-to-deploy"

[[exemptions.cfg_aliases]]
version = "0.2.0"
criteria = "safe-to-deploy"

[[exemptions.chrono]]
version = "0.4.34"
criteria = "safe-to-deploy"

[[exemptions.cocoa]]
version = "0.25.0"
criteria = "safe-to-deploy"

[[exemptions.cocoa-foundation]]
version = "0.1.2"
criteria = "safe-to-deploy"

[[exemptions.combine]]
version = "4.6.6"
criteria = "safe-to-deploy"

[[exemptions.console]]
version = "0.15.8"
criteria = "safe-to-run"

[[exemptions.core-foundation]]
version = "0.9.4"
criteria = "safe-to-deploy"

[[exemptions.core-graphics-types]]
version = "0.1.3"
criteria = "safe-to-deploy"

[[exemptions.crc32fast]]
version = "1.4.0"
criteria = "safe-to-deploy"

[[exemptions.crossbeam-channel]]
version = "0.5.12"
criteria = "safe-to-deploy"

[[exemptions.crossbeam-utils]]
version = "0.8.19"
criteria = "safe-to-deploy"

[[exemptions.cssparser]]
version = "0.27.2"
criteria = "safe-to-deploy"

[[exemptions.ctor]]
version = "0.2.7"
criteria = "safe-to-deploy"

[[exemptions.ctr]]
version = "0.9.2"
criteria = "safe-to-deploy"

[[exemptions.darling]]
version = "0.20.8"
criteria = "safe-to-deploy"

[[exemptions.darling_core]]
version = "0.20.8"
criteria = "safe-to-deploy"

[[exemptions.darling_macro]]
version = "0.20.8"
criteria = "safe-to-deploy"

[[exemptions.data-url]]
version = "0.3.1"
criteria = "safe-to-deploy"

[[exemptions.deranged]]
version = "0.3.11"
criteria = "safe-to-deploy"

[[exemptions.dirs-sys-next]]
version = "0.1.2"
criteria = "safe-to-deploy"

[[exemptions.dispatch]]
version = "0.2.0"
criteria = "safe-to-deploy"

[[exemptions.dlib]]
version = "0.5.2"
criteria = "safe-to-deploy"

[[exemptions.dlopen2]]
version = "0.7.0"
criteria = "safe-to-deploy"

[[exemptions.dlopen2_derive]]
version = "0.4.0"
criteria = "safe-to-deploy"

[[exemptions.downcast-rs]]
version = "1.2.0"
criteria = "safe-to-deploy"

[[exemptions.drm]]
version = "0.11.1"
criteria = "safe-to-deploy"

[[exemptions.drm-ffi]]
version = "0.7.1"
criteria = "safe-to-deploy"

[[exemptions.drm-fourcc]]
version = "2.2.0"
criteria = "safe-to-deploy"

[[exemptions.drm-sys]]
version = "0.6.1"
criteria = "safe-to-deploy"

[[exemptions.dtoa-short]]
version = "0.3.4"
criteria = "safe-to-deploy"

[[exemptions.dunce]]
version = "1.0.4"
criteria = "safe-to-deploy"

[[exemptions.embed-resource]]
version = "2.4.1"
criteria = "safe-to-deploy"

[[exemptions.embed_plist]]
version = "1.2.2"
criteria = "safe-to-deploy"

[[exemptions.encode_unicode]]
version = "0.3.6"
criteria = "safe-to-run"

[[exemptions.fdeflate]]
version = "0.3.4"
criteria = "safe-to-deploy"

[[exemptions.field-offset]]
version = "0.3.6"
criteria = "safe-to-deploy"

[[exemptions.flate2]]
version = "1.0.28"
criteria = "safe-to-deploy"

[[exemptions.futf]]
version = "0.1.5"
criteria = "safe-to-deploy"

[[exemptions.futures-executor]]
version = "0.3.30"
criteria = "safe-to-deploy"

[[exemptions.futures-io]]
version = "0.3.30"
criteria = "safe-to-deploy"

[[exemptions.futures-macro]]
version = "0.3.30"
criteria = "safe-to-deploy"

[[exemptions.futures-sink]]
version = "0.3.30"
criteria = "safe-to-deploy"

[[exemptions.futures-task]]
version = "0.3.30"
criteria = "safe-to-deploy"

[[exemptions.futures-util]]
version = "0.3.30"
criteria = "safe-to-deploy"

[[exemptions.gdk]]
version = "0.18.0"
criteria = "safe-to-deploy"

[[exemptions.gdk-pixbuf]]
version = "0.18.5"
criteria = "safe-to-deploy"

[[exemptions.gdk-pixbuf-sys]]
version = "0.18.0"
criteria = "safe-to-deploy"

[[exemptions.gdk-sys]]
version = "0.18.0"
criteria = "safe-to-deploy"

[[exemptions.gdkwayland-sys]]
version = "0.18.0"
criteria = "safe-to-deploy"

[[exemptions.gdkx11]]
version = "0.18.0"
criteria = "safe-to-deploy"

[[exemptions.gdkx11-sys]]
version = "0.18.0"
criteria = "safe-to-deploy"

[[exemptions.generator]]
version = "0.7.6"
criteria = "safe-to-deploy"

[[exemptions.generic-array]]
version = "0.14.7"
criteria = "safe-to-deploy"

[[exemptions.gethostname]]
version = "0.4.3"
criteria = "safe-to-deploy"

[[exemptions.getrandom]]
version = "0.1.16"
criteria = "safe-to-deploy"

[[exemptions.getrandom]]
version = "0.2.12"
criteria = "safe-to-deploy"

[[exemptions.gimli]]
version = "0.28.1"
criteria = "safe-to-deploy"

[[exemptions.gio]]
version = "0.18.4"
criteria = "safe-to-deploy"

[[exemptions.gio-sys]]
version = "0.18.1"
criteria = "safe-to-deploy"

[[exemptions.glib]]
version = "0.18.5"
criteria = "safe-to-deploy"

[[exemptions.glib-macros]]
version = "0.18.5"
criteria = "safe-to-deploy"

[[exemptions.glib-sys]]
version = "0.18.1"
criteria = "safe-to-deploy"

[[exemptions.gobject-sys]]
version = "0.18.0"
criteria = "safe-to-deploy"

[[exemptions.gtk]]
version = "0.18.1"
criteria = "safe-to-deploy"

[[exemptions.gtk-sys]]
version = "0.18.0"
criteria = "safe-to-deploy"

[[exemptions.gtk3-macros]]
version = "0.18.0"
criteria = "safe-to-deploy"

[[exemptions.hermit-abi]]
version = "0.3.8"
criteria = "safe-to-deploy"

[[exemptions.html5ever]]
version = "0.26.0"
criteria = "safe-to-deploy"

[[exemptions.http-range]]
version = "0.1.5"
criteria = "safe-to-deploy"

[[exemptions.hyper-rustls]]
version = "0.24.2"
criteria = "safe-to-deploy"

[[exemptions.iana-time-zone]]
version = "0.1.60"
criteria = "safe-to-deploy"

[[exemptions.ico]]
version = "0.3.0"
criteria = "safe-to-deploy"

[[exemptions.image]]
version = "0.24.9"
criteria = "safe-to-deploy"

[[exemptions.infer]]
version = "0.15.0"
criteria = "safe-to-deploy"

[[exemptions.insta]]
version = "1.35.1"
criteria = "safe-to-run"

[[exemptions.instant]]
version = "0.1.12"
criteria = "safe-to-deploy"

[[exemptions.ipnet]]
version = "2.9.0"
criteria = "safe-to-deploy"

[[exemptions.jni-sys]]
version = "0.3.0"
criteria = "safe-to-deploy"

[[exemptions.json-patch]]
version = "1.2.0"
criteria = "safe-to-deploy"

[[exemptions.json5]]
version = "0.4.1"
criteria = "safe-to-deploy"

[[exemptions.keyboard-types]]
version = "0.7.0"
criteria = "safe-to-deploy"

[[exemptions.kuchikiki]]
version = "0.8.2"
criteria = "safe-to-deploy"

[[exemptions.libloading]]
version = "0.7.4"
criteria = "safe-to-deploy"

[[exemptions.libloading]]
version = "0.8.1"
criteria = "safe-to-deploy"

[[exemptions.libredox]]
version = "0.0.1"
criteria = "safe-to-deploy"

[[exemptions.libxdo]]
version = "0.6.0"
criteria = "safe-to-deploy"

[[exemptions.libxdo-sys]]
version = "0.11.0"
criteria = "safe-to-deploy"

[[exemptions.mac]]
version = "0.1.1"
criteria = "safe-to-deploy"

[[exemptions.markup5ever]]
version = "0.11.0"
criteria = "safe-to-deploy"

[[exemptions.memmap2]]
version = "0.9.4"
criteria = "safe-to-deploy"

[[exemptions.memoffset]]
version = "0.9.0"
criteria = "safe-to-deploy"

[[exemptions.miniz_oxide]]
version = "0.7.2"
criteria = "safe-to-deploy"

[[exemptions.mio]]
version = "0.8.11"
criteria = "safe-to-deploy"

[[exemptions.ndk]]
version = "0.7.0"
criteria = "safe-to-deploy"

[[exemptions.ndk-sys]]
version = "0.4.1+23.1.7779620"
criteria = "safe-to-deploy"

[[exemptions.nodrop]]
version = "0.1.14"
criteria = "safe-to-deploy"

[[exemptions.num-conv]]
version = "0.1.0"
criteria = "safe-to-deploy"

[[exemptions.objc]]
version = "0.2.7"
criteria = "safe-to-deploy"

[[exemptions.objc_exception]]
version = "0.1.2"
criteria = "safe-to-deploy"

[[exemptions.objc_id]]
version = "0.1.1"
criteria = "safe-to-deploy"

[[exemptions.object]]
version = "0.32.2"
criteria = "safe-to-deploy"

[[exemptions.once_cell]]
version = "1.19.0"
criteria = "safe-to-deploy"

[[exemptions.openssl]]
version = "0.10.64"
criteria = "safe-to-deploy"

[[exemptions.openssl-sys]]
version = "0.9.101"
criteria = "safe-to-deploy"

[[exemptions.pango]]
version = "0.18.3"
criteria = "safe-to-deploy"

[[exemptions.pango-sys]]
version = "0.18.0"
criteria = "safe-to-deploy"

[[exemptions.pest]]
version = "2.7.7"
criteria = "safe-to-deploy"

[[exemptions.pest_derive]]
version = "2.7.7"
criteria = "safe-to-deploy"

[[exemptions.pest_generator]]
version = "2.7.7"
criteria = "safe-to-deploy"

[[exemptions.pest_meta]]
version = "2.7.7"
criteria = "safe-to-deploy"

[[exemptions.phf]]
version = "0.8.0"
criteria = "safe-to-deploy"

[[exemptions.phf_codegen]]
version = "0.8.0"
criteria = "safe-to-deploy"

[[exemptions.phf_generator]]
version = "0.8.0"
criteria = "safe-to-deploy"

[[exemptions.phf_macros]]
version = "0.8.0"
criteria = "safe-to-deploy"

[[exemptions.phf_shared]]
version = "0.8.0"
criteria = "safe-to-deploy"

[[exemptions.pkg-config]]
version = "0.3.30"
criteria = "safe-to-deploy"

[[exemptions.plist]]
version = "1.6.0"
criteria = "safe-to-deploy"

[[exemptions.png]]
version = "0.17.13"
criteria = "safe-to-deploy"

[[exemptions.powerfmt]]
version = "0.2.0"
criteria = "safe-to-deploy"

[[exemptions.ppv-lite86]]
version = "0.2.17"
criteria = "safe-to-deploy"

[[exemptions.proc-macro-crate]]
version = "1.3.1"
criteria = "safe-to-deploy"

[[exemptions.proc-macro-crate]]
version = "2.0.2"
criteria = "safe-to-deploy"

[[exemptions.proc-macro-error]]
version = "1.0.4"
criteria = "safe-to-deploy"

[[exemptions.proptest]]
version = "1.4.0"
criteria = "safe-to-run"

[[exemptions.quick-error]]
version = "1.2.3"
criteria = "safe-to-run"

[[exemptions.quick-xml]]
version = "0.31.0"
criteria = "safe-to-deploy"

[[exemptions.rand]]
version = "0.7.3"
criteria = "safe-to-deploy"

[[exemptions.rand]]
version = "0.8.5"
criteria = "safe-to-deploy"

[[exemptions.rand_chacha]]
version = "0.2.2"
criteria = "safe-to-deploy"

[[exemptions.rand_core]]
version = "0.5.1"
criteria = "safe-to-deploy"

[[exemptions.rand_hc]]
version = "0.2.0"
criteria = "safe-to-deploy"

[[exemptions.rand_pcg]]
version = "0.2.1"
criteria = "safe-to-deploy"

[[exemptions.redox_syscall]]
version = "0.4.1"
criteria = "safe-to-deploy"

[[exemptions.redox_users]]
version = "0.4.4"
criteria = "safe-to-deploy"

[[exemptions.ring]]
version = "0.17.8"
criteria = "safe-to-deploy"

[[exemptions.rustls]]
version = "0.21.10"
criteria = "safe-to-deploy"

[[exemptions.rustls-pemfile]]
version = "1.0.4"
criteria = "safe-to-deploy"

[[exemptions.rustls-webpki]]
version = "0.101.7"
criteria = "safe-to-deploy"

[[exemptions.rusty-fork]]
version = "0.3.0"
criteria = "safe-to-run"

[[exemptions.safemem]]
version = "0.3.3"
criteria = "safe-to-deploy"

[[exemptions.schannel]]
version = "0.1.23"
criteria = "safe-to-deploy"

[[exemptions.schemars]]
version = "0.8.16"
criteria = "safe-to-deploy"

[[exemptions.schemars_derive]]
version = "0.8.16"
criteria = "safe-to-deploy"

[[exemptions.sct]]
version = "0.7.1"
criteria = "safe-to-deploy"

[[exemptions.security-framework]]
version = "2.9.2"
criteria = "safe-to-deploy"

[[exemptions.security-framework-sys]]
version = "2.9.1"
criteria = "safe-to-deploy"

[[exemptions.serde_urlencoded]]
version = "0.7.1"
criteria = "safe-to-deploy"

[[exemptions.serde_with]]
version = "3.6.1"
criteria = "safe-to-deploy"

[[exemptions.serde_with_macros]]
version = "3.6.1"
criteria = "safe-to-deploy"

[[exemptions.sha2]]
version = "0.10.8"
criteria = "safe-to-deploy"

[[exemptions.simd-adler32]]
version = "0.3.7"
criteria = "safe-to-deploy"

[[exemptions.similar]]
version = "2.4.0"
criteria = "safe-to-run"

[[exemptions.siphasher]]
version = "0.3.11"
criteria = "safe-to-deploy"

[[exemptions.socket2]]
version = "0.5.6"
criteria = "safe-to-deploy"

[[exemptions.softbuffer]]
version = "0.4.1"
criteria = "safe-to-deploy"

[[exemptions.soup3]]
version = "0.5.0"
criteria = "safe-to-deploy"

[[exemptions.soup3-sys]]
version = "0.5.0"
criteria = "safe-to-deploy"

[[exemptions.spin]]
version = "0.9.8"
criteria = "safe-to-deploy"

[[exemptions.stable_deref_trait]]
version = "1.2.0"
criteria = "safe-to-deploy"

[[exemptions.state]]
version = "0.6.0"
criteria = "safe-to-deploy"

[[exemptions.string_cache]]
version = "0.8.7"
criteria = "safe-to-deploy"

[[exemptions.string_cache_codegen]]
version = "0.5.2"
criteria = "safe-to-deploy"

[[exemptions.swift-rs]]
version = "1.0.6"
criteria = "safe-to-deploy"

[[exemptions.sync_wrapper]]
version = "0.1.2"
criteria = "safe-to-deploy"

[[exemptions.system-configuration]]
version = "0.5.1"
criteria = "safe-to-deploy"

[[exemptions.system-configuration-sys]]
version = "0.5.0"
criteria = "safe-to-deploy"

[[exemptions.system-deps]]
version = "6.2.0"
criteria = "safe-to-deploy"

[[exemptions.tempfile]]
version = "3.10.1"
criteria = "safe-to-deploy"

[[exemptions.tendril]]
version = "0.4.3"
criteria = "safe-to-deploy"

[[exemptions.thin-slice]]
version = "0.1.1"
criteria = "safe-to-deploy"

[[exemptions.time]]
version = "0.3.34"
criteria = "safe-to-deploy"

[[exemptions.time-macros]]
version = "0.2.17"
criteria = "safe-to-deploy"

[[exemptions.tiny-xlib]]
version = "0.2.2"
criteria = "safe-to-deploy"

[[exemptions.tokio-rustls]]
version = "0.24.1"
criteria = "safe-to-deploy"

[[exemptions.tower-service]]
version = "0.3.2"
criteria = "safe-to-deploy"

[[exemptions.tracing]]
version = "0.1.40"
criteria = "safe-to-deploy"

[[exemptions.tracing-attributes]]
version = "0.1.27"
criteria = "safe-to-deploy"

[[exemptions.tracing-core]]
version = "0.1.32"
criteria = "safe-to-deploy"

[[exemptions.tracing-log]]
version = "0.2.0"
criteria = "safe-to-deploy"

[[exemptions.treediff]]
version = "4.0.3"
criteria = "safe-to-deploy"

[[exemptions.typenum]]
version = "1.17.0"
criteria = "safe-to-deploy"

[[exemptions.unarray]]
version = "0.1.4"
criteria = "safe-to-run"

[[exemptions.untrusted]]
version = "0.9.0"
criteria = "safe-to-deploy"

[[exemptions.utf-8]]
version = "0.7.6"
criteria = "safe-to-deploy"

[[exemptions.uuid]]
version = "1.7.0"
criteria = "safe-to-deploy"

[[exemptions.vswhom]]
version = "0.1.0"
criteria = "safe-to-deploy"

[[exemptions.vswhom-sys]]
version = "0.1.2"
criteria = "safe-to-deploy"

[[exemptions.wait-timeout]]
version = "0.2.0"
criteria = "safe-to-run"

[[exemptions.wasm-streams]]
version = "0.4.0"
criteria = "safe-to-deploy"

[[exemptions.wayland-backend]]
version = "0.3.3"
criteria = "safe-to-deploy"

[[exemptions.wayland-client]]
version = "0.31.2"
criteria = "safe-to-deploy"

[[exemptions.wayland-scanner]]
version = "0.31.1"
criteria = "safe-to-deploy"

[[exemptions.wayland-sys]]
version = "0.31.1"
criteria = "safe-to-deploy"

[[exemptions.webpki-roots]]
version = "0.25.4"
criteria = "safe-to-deploy"

[[exemptions.webview2-com]]
version = "0.28.0"
criteria = "safe-to-deploy"

[[exemptions.webview2-com-macros]]
version = "0.7.0"
criteria = "safe-to-deploy"

[[exemptions.webview2-com-sys]]
version = "0.28.0"
criteria = "safe-to-deploy"

[[exemptions.winapi]]
version = "0.3.9"
criteria = "safe-to-deploy"

[[exemptions.winapi-i686-pc-windows-gnu]]
version = "0.4.0"
criteria = "safe-to-deploy"

[[exemptions.winapi-x86_64-pc-windows-gnu]]
version = "0.4.0"
criteria = "safe-to-deploy"

[[exemptions.winreg]]
version = "0.50.0"
criteria = "safe-to-deploy"

[[exemptions.winreg]]
version = "0.51.0"
criteria = "safe-to-deploy"

[[exemptions.x11]]
version = "2.21.0"
criteria = "safe-to-deploy"

[[exemptions.x11-dl]]
version = "2.21.0"
criteria = "safe-to-deploy"

[[exemptions.x11rb]]
version = "0.13.0"
criteria = "safe-to-deploy"

[[exemptions.x11rb-protocol]]
version = "0.13.0"
criteria = "safe-to-deploy"
