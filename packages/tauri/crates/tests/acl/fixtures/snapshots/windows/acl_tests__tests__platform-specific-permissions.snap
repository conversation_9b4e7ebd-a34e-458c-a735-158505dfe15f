---
source: crates/tests/acl/src/lib.rs
expression: resolved
---
Resolved {
    allowed_commands: {
        "plugin:os|spawn": [
            ResolvedCommand {
                context: Local,
                windows: [
                    Pattern {
                        original: "main",
                        tokens: [
                            Char(
                                'm',
                            ),
                            <PERSON>r(
                                'a',
                            ),
                            <PERSON>r(
                                'i',
                            ),
                            Char(
                                'n',
                            ),
                        ],
                        is_recursive: false,
                    },
                ],
                webviews: [],
                scope_id: Some(
                    1,
                ),
            },
        ],
    },
    denied_commands: {},
    command_scope: {
        1: ResolvedScope {
            allow: [
                Map(
                    {
                        "command": String(
                            "edge",
                        ),
                    },
                ),
            ],
            deny: [],
        },
    },
    global_scope: {
        "os": ResolvedScope {
            allow: [],
            deny: [
                Map(
                    {
                        "path": String(
                            "$APP/EBWebView/**",
                        ),
                    },
                ),
            ],
        },
    },
}
