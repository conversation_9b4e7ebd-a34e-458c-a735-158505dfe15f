## Default Permission

Default permissions for the plugin, which enables all commands.

#### This default permission set includes the following:

- `allow-listen`
- `allow-unlisten`
- `allow-emit`
- `allow-emit-to`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`core:event:allow-emit`

</td>
<td>

Enables the emit command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:event:deny-emit`

</td>
<td>

Denies the emit command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:event:allow-emit-to`

</td>
<td>

Enables the emit_to command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:event:deny-emit-to`

</td>
<td>

Denies the emit_to command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:event:allow-listen`

</td>
<td>

Enables the listen command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:event:deny-listen`

</td>
<td>

Denies the listen command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:event:allow-unlisten`

</td>
<td>

Enables the unlisten command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:event:deny-unlisten`

</td>
<td>

Denies the unlisten command without any pre-configured scope.

</td>
</tr>
</table>
