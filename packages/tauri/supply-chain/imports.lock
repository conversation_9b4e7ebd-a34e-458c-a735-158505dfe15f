
# cargo-vet imports lock

[[publisher.aead]]
version = "0.5.2"
when = "2023-04-02"
user-id = 267
user-login = "tarcieri"
user-name = "<PERSON>"

[[publisher.aes]]
version = "0.8.4"
when = "2024-02-13"
user-id = 267
user-login = "tarcieri"
user-name = "<PERSON>"

[[publisher.aes-gcm]]
version = "0.10.3"
when = "2023-09-21"
user-id = 267
user-login = "tarcieri"
user-name = "<PERSON>"

[[publisher.aho-corasick]]
version = "1.1.2"
when = "2023-10-09"
user-id = 189
user-login = "BurntSushi"
user-name = "<PERSON>"

[[publisher.aho-corasick]]
version = "1.1.3"
when = "2024-03-20"
user-id = 189
user-login = "BurntSushi"
user-name = "<PERSON>"

[[publisher.anyhow]]
version = "1.0.80"
when = "2024-02-19"
user-id = 3618
user-login = "dtolnay"
user-name = "<PERSON>"

[[publisher.anyhow]]
version = "1.0.83"
when = "2024-05-06"
user-id = 3618
user-login = "dtolnay"
user-name = "<PERSON> Tolnay"

[[publisher.autocfg]]
version = "1.3.0"
when = "2024-05-03"
user-id = 539
user-login = "cuviper"
user-name = "Josh Stone"

[[publisher.backtrace]]
version = "0.3.69"
when = "2023-08-22"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.backtrace]]
version = "0.3.71"
when = "2024-03-22"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.bumpalo]]
version = "3.15.3"
when = "2024-02-22"
user-id = 696
user-login = "fitzgen"
user-name = "Nick Fitzgerald"

[[publisher.bumpalo]]
version = "3.15.4"
when = "2024-03-07"
user-id = 696
user-login = "fitzgen"
user-name = "Nick Fitzgerald"

[[publisher.byteorder]]
version = "1.5.0"
when = "2023-10-06"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.bytes]]
version = "1.5.0"
when = "2023-09-07"
user-id = 6741
user-login = "Darksonn"
user-name = "Alice Ryhl"

[[publisher.bytes]]
version = "1.6.0"
when = "2024-03-22"
user-id = 6741
user-login = "Darksonn"
user-name = "Alice Ryhl"

[[publisher.cc]]
version = "1.0.88"
when = "2024-02-25"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.cfg-expr]]
version = "0.15.7"
when = "2024-02-09"
user-id = 52553
user-login = "embark-studios"

[[publisher.cfg-expr]]
version = "0.15.8"
when = "2024-04-10"
user-id = 52553
user-login = "embark-studios"

[[publisher.cipher]]
version = "0.3.0"
when = "2021-04-28"
user-id = 267
user-login = "tarcieri"
user-name = "Tony Arcieri"

[[publisher.core-foundation-sys]]
version = "0.8.4"
when = "2023-04-03"
user-id = 5946
user-login = "jrmuizel"
user-name = "Jeff Muizelaar"

[[publisher.core-graphics]]
version = "0.22.3"
when = "2021-11-02"
user-id = 5946
user-login = "jrmuizel"
user-name = "Jeff Muizelaar"

[[publisher.cpufeatures]]
version = "0.2.7"
when = "2023-04-20"
user-id = 267
user-login = "tarcieri"
user-name = "Tony Arcieri"

[[publisher.digest]]
version = "0.10.7"
when = "2023-05-19"
user-id = 267
user-login = "tarcieri"
user-name = "Tony Arcieri"

[[publisher.dtoa]]
version = "1.0.9"
when = "2023-07-15"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.dyn-clone]]
version = "1.0.17"
when = "2024-02-26"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.ghash]]
version = "0.5.1"
when = "2024-03-03"
user-id = 267
user-login = "tarcieri"
user-name = "Tony Arcieri"

[[publisher.hashbrown]]
version = "0.14.3"
when = "2023-11-26"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.hashbrown]]
version = "0.14.5"
when = "2024-04-28"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.http]]
version = "0.2.11"
when = "2023-11-13"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.http]]
version = "1.1.0"
when = "2024-03-04"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.http-body]]
version = "0.4.6"
when = "2023-12-08"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.http-body]]
version = "1.0.0"
when = "2023-11-15"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.http-body-util]]
version = "0.1.1"
when = "2024-03-11"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.httparse]]
version = "1.8.0"
when = "2022-08-30"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.hyper]]
version = "0.14.28"
when = "2023-12-18"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.hyper]]
version = "1.3.1"
when = "2024-04-16"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.hyper-tls]]
version = "0.5.0"
when = "2020-12-29"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.hyper-tls]]
version = "0.6.0"
when = "2023-11-27"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.hyper-util]]
version = "0.1.3"
when = "2024-01-31"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.indexmap]]
version = "1.9.3"
when = "2023-03-24"
user-id = 539
user-login = "cuviper"
user-name = "Josh Stone"

[[publisher.indexmap]]
version = "2.2.3"
when = "2024-02-11"
user-id = 539
user-login = "cuviper"
user-name = "Josh Stone"

[[publisher.indexmap]]
version = "2.2.6"
when = "2024-03-23"
user-id = 539
user-login = "cuviper"
user-name = "Josh Stone"

[[publisher.itoa]]
version = "0.4.8"
when = "2021-08-22"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.itoa]]
version = "1.0.10"
when = "2023-12-09"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.itoa]]
version = "1.0.11"
when = "2024-03-26"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.javascriptcore-rs]]
version = "1.1.2"
when = "2023-10-26"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.javascriptcore-rs-sys]]
version = "1.1.1"
when = "2023-10-26"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.js-sys]]
version = "0.3.68"
when = "2024-02-06"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.js-sys]]
version = "0.3.69"
when = "2024-03-04"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.libappindicator]]
version = "0.9.0"
when = "2023-10-01"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.libappindicator-sys]]
version = "0.9.0"
when = "2023-10-01"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.libc]]
version = "0.2.146"
when = "2023-06-06"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.libc]]
version = "0.2.154"
when = "2024-04-29"
user-id = 51017
user-login = "JohnTitor"
user-name = "Yuki Okushi"

[[publisher.libm]]
version = "0.2.8"
when = "2023-10-06"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.linux-raw-sys]]
version = "0.4.13"
when = "2024-01-16"
user-id = 6825
user-login = "sunfishcode"
user-name = "Dan Gohman"

[[publisher.linux-raw-sys]]
version = "0.6.4"
when = "2024-01-17"
user-id = 6825
user-login = "sunfishcode"
user-name = "Dan Gohman"

[[publisher.lock_api]]
version = "0.4.11"
when = "2023-10-17"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.lock_api]]
version = "0.4.12"
when = "2024-04-25"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.loom]]
version = "0.5.6"
when = "2022-05-19"
user-id = 6741
user-login = "Darksonn"
user-name = "Alice Ryhl"

[[publisher.memchr]]
version = "2.7.1"
when = "2023-12-28"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.memchr]]
version = "2.7.2"
when = "2024-03-27"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.mime]]
version = "0.3.17"
when = "2023-03-20"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.muda]]
version = "0.13.2"
when = "2024-05-07"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.new_debug_unreachable]]
version = "1.0.6"
when = "2024-03-15"
user-id = 2017
user-login = "mbrubeck"
user-name = "Matt Brubeck"

[[publisher.num_cpus]]
version = "1.16.0"
when = "2023-06-29"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.openssl-src]]
version = "300.2.3+3.2.1"
when = "2024-02-12"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.parking_lot]]
version = "0.12.1"
when = "2022-05-31"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.parking_lot]]
version = "0.12.2"
when = "2024-04-25"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.parking_lot_core]]
version = "0.9.9"
when = "2023-10-17"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.parking_lot_core]]
version = "0.9.10"
when = "2024-04-25"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.paste]]
version = "1.0.15"
when = "2024-05-07"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.phf]]
version = "0.10.1"
when = "2021-12-13"
user-id = 51017
user-login = "JohnTitor"
user-name = "Yuki Okushi"

[[publisher.phf]]
version = "0.11.2"
when = "2023-06-24"
user-id = 51017
user-login = "JohnTitor"
user-name = "Yuki Okushi"

[[publisher.phf_codegen]]
version = "0.10.0"
when = "2021-08-10"
user-id = 51017
user-login = "JohnTitor"
user-name = "Yuki Okushi"

[[publisher.phf_generator]]
version = "0.10.0"
when = "2021-08-10"
user-id = 51017
user-login = "JohnTitor"
user-name = "Yuki Okushi"

[[publisher.phf_generator]]
version = "0.11.2"
when = "2023-06-24"
user-id = 51017
user-login = "JohnTitor"
user-name = "Yuki Okushi"

[[publisher.phf_macros]]
version = "0.11.2"
when = "2023-06-24"
user-id = 51017
user-login = "JohnTitor"
user-name = "Yuki Okushi"

[[publisher.phf_shared]]
version = "0.10.0"
when = "2021-08-10"
user-id = 51017
user-login = "JohnTitor"
user-name = "Yuki Okushi"

[[publisher.phf_shared]]
version = "0.11.2"
when = "2023-06-24"
user-id = 51017
user-login = "JohnTitor"
user-name = "Yuki Okushi"

[[publisher.polyval]]
version = "0.6.2"
when = "2024-03-03"
user-id = 267
user-login = "tarcieri"
user-name = "Tony Arcieri"

[[publisher.proc-macro-hack]]
version = "0.5.20+deprecated"
when = "2022-12-19"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.proc-macro2]]
version = "1.0.78"
when = "2024-01-21"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.proc-macro2]]
version = "1.0.82"
when = "2024-05-07"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.quickcheck]]
version = "1.0.3"
when = "2021-01-15"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.regex]]
version = "1.10.3"
when = "2024-01-21"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.regex]]
version = "1.10.4"
when = "2024-03-23"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.regex-automata]]
version = "0.1.10"
when = "2021-06-01"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.regex-automata]]
version = "0.4.5"
when = "2024-01-25"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.regex-automata]]
version = "0.4.6"
when = "2024-03-04"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.regex-syntax]]
version = "0.6.29"
when = "2023-03-21"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.regex-syntax]]
version = "0.8.2"
when = "2023-10-14"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.regex-syntax]]
version = "0.8.3"
when = "2024-03-26"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.reqwest]]
version = "0.11.24"
when = "2024-01-31"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.reqwest]]
version = "0.12.4"
when = "2024-04-19"
user-id = 359
user-login = "seanmonstar"
user-name = "Sean McArthur"

[[publisher.rustix]]
version = "0.38.31"
when = "2024-02-01"
user-id = 6825
user-login = "sunfishcode"
user-name = "Dan Gohman"

[[publisher.rustix]]
version = "0.38.34"
when = "2024-04-22"
user-id = 6825
user-login = "sunfishcode"
user-name = "Dan Gohman"

[[publisher.rustversion]]
version = "1.0.16"
when = "2024-05-07"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.ryu]]
version = "1.0.17"
when = "2024-02-19"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.ryu]]
version = "1.0.18"
when = "2024-05-07"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.same-file]]
version = "1.0.6"
when = "2020-01-11"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.scoped-tls]]
version = "1.0.1"
when = "2022-10-31"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.scopeguard]]
version = "1.2.0"
when = "2023-07-17"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.semver]]
version = "1.0.22"
when = "2024-02-19"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.semver]]
version = "1.0.23"
when = "2024-05-07"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.serde]]
version = "1.0.197"
when = "2024-02-20"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.serde]]
version = "1.0.201"
when = "2024-05-08"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.serde_derive]]
version = "1.0.201"
when = "2024-05-08"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.serde_derive_internals]]
version = "0.26.0"
when = "2021-04-09"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.serde_derive_internals]]
version = "0.29.0"
when = "2023-09-06"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.serde_json]]
version = "1.0.114"
when = "2024-02-20"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.serde_json]]
version = "1.0.117"
when = "2024-05-08"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.serde_repr]]
version = "0.1.18"
when = "2024-01-02"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.serde_repr]]
version = "0.1.19"
when = "2024-04-08"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.serde_spanned]]
version = "0.6.5"
when = "2023-12-19"
user-id = 6743
user-login = "epage"
user-name = "Ed Page"

[[publisher.serialize-to-javascript]]
version = "0.1.1"
when = "2022-02-08"
user-id = 28029
user-login = "chippers"
user-name = "chip"

[[publisher.serialize-to-javascript-impl]]
version = "0.1.1"
when = "2022-02-08"
user-id = 28029
user-login = "chippers"
user-name = "chip"

[[publisher.slab]]
version = "0.4.9"
when = "2023-08-22"
user-id = 6741
user-login = "Darksonn"
user-name = "Alice Ryhl"

[[publisher.smallvec]]
version = "1.13.1"
when = "2024-01-19"
user-id = 2017
user-login = "mbrubeck"
user-name = "Matt Brubeck"

[[publisher.smallvec]]
version = "1.13.2"
when = "2024-03-20"
user-id = 2017
user-login = "mbrubeck"
user-name = "Matt Brubeck"

[[publisher.syn]]
version = "1.0.109"
when = "2023-02-24"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.syn]]
version = "2.0.51"
when = "2024-02-26"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.syn]]
version = "2.0.61"
when = "2024-05-07"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.tao]]
version = "0.28.0"
when = "2024-05-07"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.tao-macros]]
version = "0.1.2"
when = "2023-08-13"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.target-lexicon]]
version = "0.12.14"
when = "2024-02-22"
user-id = 6825
user-login = "sunfishcode"
user-name = "Dan Gohman"

[[publisher.tauri]]
version = "2.0.0-beta.18"
when = "2024-05-06"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.tauri-build]]
version = "2.0.0-beta.14"
when = "2024-05-06"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.tauri-codegen]]
version = "2.0.0-beta.14"
when = "2024-05-06"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.tauri-macros]]
version = "2.0.0-beta.14"
when = "2024-05-06"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.tauri-plugin]]
version = "2.0.0-beta.14"
when = "2024-05-06"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.tauri-runtime]]
version = "2.0.0-beta.15"
when = "2024-05-06"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.tauri-runtime-wry]]
version = "2.0.0-beta.15"
when = "2024-05-06"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.tauri-utils]]
version = "2.0.0-beta.14"
when = "2024-05-06"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.tauri-winres]]
version = "0.1.1"
when = "2023-05-04"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.thiserror]]
version = "1.0.57"
when = "2024-02-11"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.thiserror]]
version = "1.0.60"
when = "2024-05-07"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.thiserror-impl]]
version = "1.0.57"
when = "2024-02-11"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.thiserror-impl]]
version = "1.0.60"
when = "2024-05-07"
user-id = 3618
user-login = "dtolnay"
user-name = "David Tolnay"

[[publisher.thread_local]]
version = "1.1.8"
when = "2024-02-20"
user-id = 2915
user-login = "Amanieu"
user-name = "Amanieu d'Antras"

[[publisher.tokio]]
version = "1.36.0"
when = "2024-02-02"
user-id = 6741
user-login = "Darksonn"
user-name = "Alice Ryhl"

[[publisher.tokio]]
version = "1.37.0"
when = "2024-03-28"
user-id = 6741
user-login = "Darksonn"
user-name = "Alice Ryhl"

[[publisher.tokio-macros]]
version = "2.2.0"
when = "2023-11-09"
user-id = 10
user-login = "carllerche"
user-name = "Carl Lerche"

[[publisher.tokio-util]]
version = "0.7.10"
when = "2023-10-25"
user-id = 6741
user-login = "Darksonn"
user-name = "Alice Ryhl"

[[publisher.tokio-util]]
version = "0.7.11"
when = "2024-05-04"
user-id = 6741
user-login = "Darksonn"
user-name = "Alice Ryhl"

[[publisher.toml]]
version = "0.7.8"
when = "2023-09-09"
user-id = 6743
user-login = "epage"
user-name = "Ed Page"

[[publisher.toml]]
version = "0.8.2"
when = "2023-10-03"
user-id = 6743
user-login = "epage"
user-name = "Ed Page"

[[publisher.toml_edit]]
version = "0.19.15"
when = "2023-09-08"
user-id = 6743
user-login = "epage"
user-name = "Ed Page"

[[publisher.toml_edit]]
version = "0.20.2"
when = "2023-10-03"
user-id = 6743
user-login = "epage"
user-name = "Ed Page"

[[publisher.tray-icon]]
version = "0.13.5"
when = "2024-05-02"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.ucd-trie]]
version = "0.1.6"
when = "2023-07-07"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.unicode-normalization]]
version = "0.1.23"
when = "2024-02-20"
user-id = 1139
user-login = "Manishearth"
user-name = "Manish Goregaokar"

[[publisher.unicode-segmentation]]
version = "1.11.0"
when = "2024-02-07"
user-id = 1139
user-login = "Manishearth"
user-name = "Manish Goregaokar"

[[publisher.walkdir]]
version = "2.4.0"
when = "2023-09-05"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.walkdir]]
version = "2.5.0"
when = "2024-03-01"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.wasi]]
version = "0.9.0+wasi-snapshot-preview1"
when = "2019-12-02"
user-id = 6825
user-login = "sunfishcode"
user-name = "Dan Gohman"

[[publisher.wasi]]
version = "0.11.0+wasi-snapshot-preview1"
when = "2022-01-19"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.wasm-bindgen]]
version = "0.2.91"
when = "2024-02-06"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.wasm-bindgen-backend]]
version = "0.2.91"
when = "2024-02-06"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.wasm-bindgen-backend]]
version = "0.2.92"
when = "2024-03-04"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.wasm-bindgen-futures]]
version = "0.4.41"
when = "2024-02-06"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.wasm-bindgen-futures]]
version = "0.4.42"
when = "2024-03-04"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.wasm-bindgen-macro]]
version = "0.2.91"
when = "2024-02-06"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.wasm-bindgen-macro]]
version = "0.2.92"
when = "2024-03-04"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.wasm-bindgen-macro-support]]
version = "0.2.91"
when = "2024-02-06"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.wasm-bindgen-shared]]
version = "0.2.91"
when = "2024-02-06"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.wasm-bindgen-shared]]
version = "0.2.92"
when = "2024-03-04"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.web-sys]]
version = "0.3.68"
when = "2024-02-06"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.web-sys]]
version = "0.3.69"
when = "2024-03-04"
user-id = 1
user-login = "alexcrichton"
user-name = "Alex Crichton"

[[publisher.webkit2gtk]]
version = "2.0.1"
when = "2023-10-26"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.webkit2gtk-sys]]
version = "2.0.1"
when = "2023-10-26"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.winapi-util]]
version = "0.1.6"
when = "2023-09-20"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.winapi-util]]
version = "0.1.8"
when = "2024-04-25"
user-id = 189
user-login = "BurntSushi"
user-name = "Andrew Gallant"

[[publisher.window-vibrancy]]
version = "0.5.0"
when = "2024-02-07"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.windows]]
version = "0.48.0"
when = "2023-03-31"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows]]
version = "0.52.0"
when = "2023-11-15"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows]]
version = "0.53.0"
when = "2024-02-22"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows]]
version = "0.56.0"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-core]]
version = "0.52.0"
when = "2023-11-15"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-core]]
version = "0.53.0"
when = "2024-02-22"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-core]]
version = "0.56.0"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-implement]]
version = "0.52.0"
when = "2023-11-15"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-implement]]
version = "0.56.0"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-interface]]
version = "0.52.0"
when = "2023-11-15"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-interface]]
version = "0.56.0"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-result]]
version = "0.1.0"
when = "2024-02-22"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-result]]
version = "0.1.1"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-sys]]
version = "0.45.0"
when = "2023-01-21"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-sys]]
version = "0.48.0"
when = "2023-03-31"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-sys]]
version = "0.52.0"
when = "2023-11-15"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-targets]]
version = "0.42.2"
when = "2023-03-13"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-targets]]
version = "0.48.5"
when = "2023-08-18"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-targets]]
version = "0.52.3"
when = "2024-02-22"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-targets]]
version = "0.52.5"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-version]]
version = "0.1.0"
when = "2023-11-15"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows-version]]
version = "0.1.1"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_aarch64_gnullvm]]
version = "0.42.2"
when = "2023-03-13"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_aarch64_gnullvm]]
version = "0.48.5"
when = "2023-08-18"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_aarch64_gnullvm]]
version = "0.52.3"
when = "2024-02-22"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_aarch64_gnullvm]]
version = "0.52.5"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_aarch64_msvc]]
version = "0.42.2"
when = "2023-03-13"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_aarch64_msvc]]
version = "0.48.5"
when = "2023-08-18"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_aarch64_msvc]]
version = "0.52.3"
when = "2024-02-22"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_aarch64_msvc]]
version = "0.52.5"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_i686_gnu]]
version = "0.42.2"
when = "2023-03-13"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_i686_gnu]]
version = "0.48.5"
when = "2023-08-18"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_i686_gnu]]
version = "0.52.3"
when = "2024-02-22"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_i686_gnu]]
version = "0.52.5"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_i686_gnullvm]]
version = "0.52.5"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_i686_msvc]]
version = "0.42.2"
when = "2023-03-13"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_i686_msvc]]
version = "0.48.5"
when = "2023-08-18"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_i686_msvc]]
version = "0.52.3"
when = "2024-02-22"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_i686_msvc]]
version = "0.52.5"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_x86_64_gnu]]
version = "0.42.2"
when = "2023-03-13"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_x86_64_gnu]]
version = "0.48.5"
when = "2023-08-18"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_x86_64_gnu]]
version = "0.52.3"
when = "2024-02-22"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_x86_64_gnu]]
version = "0.52.5"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_x86_64_gnullvm]]
version = "0.42.2"
when = "2023-03-13"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_x86_64_gnullvm]]
version = "0.48.5"
when = "2023-08-18"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_x86_64_gnullvm]]
version = "0.52.3"
when = "2024-02-22"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_x86_64_gnullvm]]
version = "0.52.5"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_x86_64_msvc]]
version = "0.42.2"
when = "2023-03-13"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_x86_64_msvc]]
version = "0.48.5"
when = "2023-08-18"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_x86_64_msvc]]
version = "0.52.3"
when = "2024-02-22"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.windows_x86_64_msvc]]
version = "0.52.5"
when = "2024-04-12"
user-id = 64539
user-login = "kennykerr"
user-name = "Kenny Kerr"

[[publisher.winnow]]
version = "0.5.40"
when = "2024-02-12"
user-id = 6743
user-login = "epage"
user-name = "Ed Page"

[[publisher.wry]]
version = "0.39.4"
when = "2024-05-07"
user-id = 95389
user-login = "tauri-bot"
user-name = "tauri"

[[publisher.zeroize]]
version = "1.7.0"
when = "2023-11-16"
user-id = 267
user-login = "tarcieri"
user-name = "Tony Arcieri"

[[audits.bytecode-alliance.wildcard-audits.bumpalo]]
who = "Nick Fitzgerald <<EMAIL>>"
criteria = "safe-to-deploy"
user-id = 696 # Nick Fitzgerald (fitzgen)
start = "2019-03-16"
end = "2024-03-10"

[[audits.bytecode-alliance.audits.adler]]
who = "Alex Crichton <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.0.2"
notes = "This is a small crate which forbids unsafe code and is a straightforward implementation of the adler hashing algorithm."

[[audits.bytecode-alliance.audits.block-buffer]]
who = "Benjamin Bouvier <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.9.0 -> 0.10.2"

[[audits.bytecode-alliance.audits.cargo_metadata]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.15.3"
notes = "no build, no unsafe, inputs to cargo command are reasonably sanitized"

[[audits.bytecode-alliance.audits.cargo_metadata]]
who = "Alex Crichton <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.17.0 -> 0.18.1"
notes = "No major changes, no unsafe code here."

[[audits.bytecode-alliance.audits.cfg-if]]
who = "Alex Crichton <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.0.0"
notes = "I am the author of this crate."

[[audits.bytecode-alliance.audits.core-foundation-sys]]
who = "Dan Gohman <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.8.4 -> 0.8.6"
notes = """
The changes here are all typical bindings updates: new functions, types, and
constants. I have not audited all the bindings for ABI conformance.
"""

[[audits.bytecode-alliance.audits.crypto-common]]
who = "Benjamin Bouvier <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.3"

[[audits.bytecode-alliance.audits.errno]]
who = "Dan Gohman <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.3.0"
notes = "This crate uses libc and windows-sys APIs to get and set the raw OS error value."

[[audits.bytecode-alliance.audits.errno]]
who = "Dan Gohman <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.0 -> 0.3.1"
notes = "Just a dependency version bump and a bug fix for redox"

[[audits.bytecode-alliance.audits.fastrand]]
who = "Alex Crichton <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "2.0.0 -> 2.0.1"
notes = """
This update had a few doc updates but no otherwise-substantial source code
updates.
"""

[[audits.bytecode-alliance.audits.foreign-types]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.3.2"
notes = "This crate defined a macro-rules which creates wrappers working with FFI types. The implementation of this crate appears to be safe, but each use of this macro would need to be vetted for correctness as well."

[[audits.bytecode-alliance.audits.foreign-types-shared]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.1"

[[audits.bytecode-alliance.audits.futures-channel]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.3.27"
notes = "build.rs is just detecting the target and setting cfg. unsafety is for implementing a concurrency primitives using atomics and unsafecell, and is not obviously incorrect (this is the sort of thing I wouldn't certify as correct without formal methods)"

[[audits.bytecode-alliance.audits.futures-core]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.3.27"
notes = "Unsafe used to implement a concurrency primitive AtomicWaker. Well-commented and not obviously incorrect. Like my other audits of these concurrency primitives inside the futures family, I couldn't certify that it is correct without formal methods, but that is out of scope for this vetting."

[[audits.bytecode-alliance.audits.heck]]
who = "Alex Crichton <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.4.0"
notes = "Contains `forbid_unsafe` and only uses `std::fmt` from the standard library. Otherwise only contains string manipulation."

[[audits.bytecode-alliance.audits.iana-time-zone-haiku]]
who = "Dan Gohman <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.2"

[[audits.bytecode-alliance.audits.idna]]
who = "Alex Crichton <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.3.0"
notes = """
This is a crate without unsafe code or usage of the standard library. The large
size of this crate comes from the large generated unicode tables file. This
crate is broadly used throughout the ecosystem and does not contain anything
suspicious.
"""

[[audits.bytecode-alliance.audits.libc]]
who = "Alex Crichton <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.146 -> 0.2.147"
notes = "Only new type definitions and updating others for some platforms, no major changes"

[[audits.bytecode-alliance.audits.libc]]
who = "Alex Crichton <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.148 -> 0.2.149"
notes = "Lots of new functions and constants for new platforms and nothing out of the ordinary for what one would expect of the `libc` crate."

[[audits.bytecode-alliance.audits.libc]]
who = "Dan Gohman <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.149 -> 0.2.151"
notes = "More new functions, types, and constants, as is usual for the `libc` crate, as well as various minor code cleanups."

[[audits.bytecode-alliance.audits.libc]]
who = "Alex Crichton <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.151 -> 0.2.153"
notes = "More bindings for more platforms. I have not verified that everything is exactly as-is on the platform as specified but nothing major is otherwise introduced as part of this bump."

[[audits.bytecode-alliance.audits.matchers]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.0"

[[audits.bytecode-alliance.audits.native-tls]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.2.11"
notes = "build is only looking for environment variables to set cfg. only two minor uses of unsafe,on macos, with ffi bindings to digest primitives and libc atexit. otherwise, this is an abstraction over three very complex systems (schannel, security-framework, and openssl) which may end up having subtle differences, but none of those are apparent from the implementation of this crate"

[[audits.bytecode-alliance.audits.nu-ansi-term]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.46.0"
notes = "one use of unsafe to call windows specific api to get console handle."

[[audits.bytecode-alliance.audits.openssl-macros]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.0"

[[audits.bytecode-alliance.audits.openssl-probe]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.5"
notes = "IO is only checking for the existence of paths in the filesystem"

[[audits.bytecode-alliance.audits.overload]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.1"
notes = "small crate, only defines macro-rules!, nicely documented as well"

[[audits.bytecode-alliance.audits.percent-encoding]]
who = "Alex Crichton <<EMAIL>>"
criteria = "safe-to-deploy"
version = "2.2.0"
notes = """
This crate is a single-file crate that does what it says on the tin. There are
a few `unsafe` blocks related to utf-8 validation which are locally verifiable
as correct and otherwise this crate is good to go.
"""

[[audits.bytecode-alliance.audits.pin-utils]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.0"

[[audits.bytecode-alliance.audits.quote]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "1.0.23 -> 1.0.27"

[[audits.bytecode-alliance.audits.rustc-demangle]]
who = "Alex Crichton <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.21"
notes = "I am the author of this crate."

[[audits.bytecode-alliance.audits.sharded-slab]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.4"
notes = "I always really enjoy reading eliza's code, she left perfect comments at every use of unsafe."

[[audits.bytecode-alliance.audits.signal-hook-registry]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.4.1"

[[audits.bytecode-alliance.audits.tinyvec]]
who = "Alex Crichton <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.6.0"
notes = """
This crate, while it implements collections, does so without `std::*` APIs and
without `unsafe`. Skimming the crate everything looks reasonable and what one
would expect from idiomatic safe collections in Rust.
"""

[[audits.bytecode-alliance.audits.tinyvec_macros]]
who = "Alex Crichton <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.0"
notes = """
This is a trivial crate which only contains a singular macro definition which is
intended to multiplex across the internal representation of a tinyvec,
presumably. This trivially doesn't contain anything bad.
"""

[[audits.bytecode-alliance.audits.tokio-native-tls]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.3.1"
notes = "unsafety is used for smuggling std::task::Context as a raw pointer. Lifetime and type safety appears to be taken care of correctly."

[[audits.bytecode-alliance.audits.tracing-subscriber]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.3.17"

[[audits.bytecode-alliance.audits.try-lock]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.2.4"
notes = "Implements a concurrency primitive with atomics, and is not obviously incorrect"

[[audits.bytecode-alliance.audits.unicode-bidi]]
who = "Alex Crichton <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.3.8"
notes = """
This crate has no unsafe code and does not use `std::*`. Skimming the crate it
does not attempt to out of the bounds of what it's already supposed to be doing.
"""

[[audits.bytecode-alliance.audits.unicode-ident]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.0.8"

[[audits.bytecode-alliance.audits.vcpkg]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.2.15"
notes = "no build.rs, no macros, no unsafe. It reads the filesystem and makes copies of DLLs into OUT_DIR."

[[audits.bytecode-alliance.audits.want]]
who = "Pat Hickey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.3.0"

[[audits.embark-studios.wildcard-audits.cfg-expr]]
who = "Jake Shadle <<EMAIL>>"
criteria = "safe-to-deploy"
user-id = 52553 # embark-studios
start = "2020-01-01"
end = "2024-05-23"
notes = "Maintained by Embark. No unsafe usage or ambient capabilities"

[[audits.embark-studios.audits.cargo_metadata]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.15.3 -> 0.15.4"
notes = "No notable changes"

[[audits.embark-studios.audits.cargo_metadata]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.15.4 -> 0.17.0"
notes = "No notable changes"

[[audits.embark-studios.audits.cfg_aliases]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.1"
notes = "No unsafe usage or ambient capabilities"

[[audits.embark-studios.audits.convert_case]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.4.0"
notes = "No unsafe usage or ambient capabilities"

[[audits.embark-studios.audits.derive_more]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.99.17"
notes = "No unsafe usage or ambient capabilities"

[[audits.embark-studios.audits.ident_case]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.0.1"
notes = "No unsafe usage or ambient capabilities"

[[audits.embark-studios.audits.idna]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.0 -> 0.4.0"
notes = "No unsafe usage or ambient capabilities"

[[audits.embark-studios.audits.jni]]
who = "Robert Bragg <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.21.1"
notes = """
Aims to provide a safe JNI (Java Native Interface) API over the
unsafe `jni_sys` crate.

This is a very general FFI abstraction for Java VMs with a lot of unsafe code
throughout the API. There are almost certainly some edge cases with its design
that could lead to unsound behaviour but it should still be considerably safer
than working with JNI directly.

A lot of the unsafe usage relates to quite-simple use of `from_raw` APIs to
construct or cast wrapper types (around JNI pointers) which are fairly
straight-forward to verify/trust in context.

Some unsafe code has good `// # Safety` documentation (this has been enforced for
newer code) but a lot of unsafe code doesn't document invariants that are
being relied on.

The design depends on non-trivial named lifetimes across many APIs to associate
Java local references with JNI stack frames.

The crate is not very actively maintained and was practically unmaintained for
over a year before the 0.20 release.

Robert Bragg who now works at Embark Studios became the maintainer of this
crate in October 2022.

In the process of working on the `jni` crate since becoming maintainer it's
worth noting that I came across multiple APIs that I found needed to be
re-worked to address safety issues, including ensuring that APIs that are not
implemented safely are correctly declared as `unsafe`.

There has been a focus on improving safety in the last two release.

The jni crate has been used in production with the Signal messaging application
for over two years:
https://github.com/signalapp/libsignal/blob/main/rust/bridge/jni/Cargo.toml

# Some Notable Open Issues
- https://github.com/jni-rs/jni-rs/issues/422 - questions soundness of linking
  multiple versions of jni crate into an application, considering the use
  of (separately scoped) thread-local-storage to track thread attachments
- https://github.com/jni-rs/jni-rs/issues/405 - discusses the ease with which
  code may expose the JVM to invalid booleans with undefined behaviour
"""

[[audits.embark-studios.audits.line-wrap]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.1"
notes = "No unsafe usage or ambient capabilities"

[[audits.embark-studios.audits.ndk-context]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.1"
notes = "Tiny crate that initializes Android with FFI, looks sane. No other ambient capabilities"

[[audits.embark-studios.audits.num_enum]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.5.11"
notes = "No unsafe usage or ambient capabilities"

[[audits.embark-studios.audits.num_enum_derive]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.5.11"
notes = "Proc macro that generates some unsafe code for conversion but looks sound, no ambient capabilities"

[[audits.embark-studios.audits.quickcheck_macros]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.0.0"
notes = "Proc macro. No unsafe usage or ambient capabilities"

[[audits.embark-studios.audits.toml_datetime]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.6.1 -> 0.6.2"
notes = "No notable changes"

[[audits.embark-studios.audits.unic-ucd-version]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.9.0"
notes = "Only few constants. No unsafe usage or other ambient capabilities"

[[audits.embark-studios.audits.valuable]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.0"
notes = "No unsafe usage or ambient capabilities, sane build script"

[[audits.embark-studios.audits.version-compare]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.1"
notes = "No unsafe usage or ambient capabilities"

[[audits.embark-studios.audits.yaml-rust]]
who = "Johan Andersson <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.4.5"
notes = "No unsafe usage or ambient capabilities"

[[audits.google.audits.bitflags]]
who = "Lukasz Anforowicz <<EMAIL>>"
criteria = "safe-to-deploy"
version = "2.4.2"
notes = """
Audit notes:

* I've checked for any discussion in Google-internal cl/546819168 (where audit
  of version 2.3.3 happened)
* `src/lib.rs` contains `#![cfg_attr(not(test), forbid(unsafe_code))]`
* There are 2 cases of `unsafe` in `src/external.rs` but they seem to be
  correct in a straightforward way - they just propagate the marker trait's
  impl (e.g. `impl bytemuck::Pod`) from the inner to the outer type
* Additional discussion and/or notes may be found in https://crrev.com/c/5238056
"""
aggregated-from = "https://chromium.googlesource.com/chromium/src/+/main/third_party/rust/chromium_crates_io/supply-chain/audits.toml?format=TEXT"

[[audits.google.audits.bitflags]]
who = "Adrian Taylor <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "2.4.2 -> 2.5.0"
aggregated-from = "https://chromium.googlesource.com/chromium/src/+/main/third_party/rust/chromium_crates_io/supply-chain/audits.toml?format=TEXT"

[[audits.google.audits.bytemuck]]
who = "Lukasz Anforowicz <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.14.3"
notes = "Additional review notes may be found in https://crrev.com/c/5362675."
aggregated-from = "https://chromium.googlesource.com/chromium/src/+/main/third_party/rust/chromium_crates_io/supply-chain/audits.toml?format=TEXT"

[[audits.google.audits.bytemuck]]
who = "Adrian Taylor <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "1.14.3 -> 1.15.0"
aggregated-from = "https://chromium.googlesource.com/chromium/src/+/main/third_party/rust/chromium_crates_io/supply-chain/audits.toml?format=TEXT"

[[audits.google.audits.color_quant]]
who = "George Burgess IV <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.1.0"
aggregated-from = "https://chromium.googlesource.com/chromiumos/third_party/rust_crates/+/refs/heads/main/cargo-vet/audits.toml?format=TEXT"

[[audits.google.audits.env_logger]]
who = "George Burgess IV <<EMAIL>>"
criteria = "safe-to-run"
version = "0.9.3"
aggregated-from = "https://chromium.googlesource.com/chromiumos/third_party/rust_crates/+/refs/heads/main/cargo-vet/audits.toml?format=TEXT"

[[audits.google.audits.env_logger]]
who = "George Burgess IV <<EMAIL>>"
criteria = "safe-to-run"
delta = "0.9.3 -> 0.8.4"
aggregated-from = "https://chromium.googlesource.com/chromiumos/third_party/rust_crates/+/refs/heads/main/cargo-vet/audits.toml?format=TEXT"

[[audits.google.audits.equivalent]]
who = "George Burgess IV <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.0.1"
aggregated-from = "https://chromium.googlesource.com/chromiumos/third_party/rust_crates/+/refs/heads/main/cargo-vet/audits.toml?format=TEXT"

[[audits.google.audits.fastrand]]
who = "George Burgess IV <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.9.0"
notes = """
`does-not-implement-crypto` is certified because this crate explicitly says
that the RNG here is not cryptographically secure.
"""
aggregated-from = "https://chromium.googlesource.com/chromiumos/third_party/rust_crates/+/refs/heads/main/cargo-vet/audits.toml?format=TEXT"

[[audits.google.audits.glob]]
who = "George Burgess IV <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.3.1"
aggregated-from = "https://chromium.googlesource.com/chromiumos/third_party/rust_crates/+/refs/heads/main/cargo-vet/audits.toml?format=TEXT"

[[audits.google.audits.httpdate]]
who = "George Burgess IV <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.0.3"
aggregated-from = "https://chromium.googlesource.com/chromiumos/third_party/rust_crates/+/refs/heads/main/cargo-vet/audits.toml?format=TEXT"

[[audits.google.audits.openssl-macros]]
who = "George Burgess IV <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.1.0 -> 0.1.1"
aggregated-from = "https://chromium.googlesource.com/chromiumos/third_party/rust_crates/+/refs/heads/main/cargo-vet/audits.toml?format=TEXT"

[[audits.google.audits.pin-project-lite]]
who = "David Koloski <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.2.9"
notes = "Reviewed on https://fxrev.dev/824504"
aggregated-from = "https://fuchsia.googlesource.com/fuchsia/+/refs/heads/main/third_party/rust_crates/supply-chain/audits.toml?format=TEXT"

[[audits.google.audits.pin-project-lite]]
who = "David Koloski <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.9 -> 0.2.13"
notes = "Audited at https://fxrev.dev/946396"
aggregated-from = "https://fuchsia.googlesource.com/fuchsia/+/refs/heads/main/third_party/rust_crates/supply-chain/audits.toml?format=TEXT"

[[audits.google.audits.proc-macro-error-attr]]
who = "George Burgess IV <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.0.4"
aggregated-from = "https://chromium.googlesource.com/chromiumos/third_party/rust_crates/+/refs/heads/main/cargo-vet/audits.toml?format=TEXT"

[[audits.google.audits.quote]]
who = "Lukasz Anforowicz <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.0.35"
notes = """
Grepped for \"unsafe\", \"crypt\", \"cipher\", \"fs\", \"net\" - there were no hits
(except for benign \"net\" hit in tests and \"fs\" hit in README.md)
"""
aggregated-from = "https://chromium.googlesource.com/chromium/src/+/main/third_party/rust/chromium_crates_io/supply-chain/audits.toml?format=TEXT"

[[audits.google.audits.quote]]
who = "Adrian Taylor <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "1.0.35 -> 1.0.36"
aggregated-from = "https://chromium.googlesource.com/chromium/src/+/main/third_party/rust/chromium_crates_io/supply-chain/audits.toml?format=TEXT"

[[audits.google.audits.serde_derive]]
who = "Lukasz Anforowicz <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.0.197"
notes = "Grepped for \"unsafe\", \"crypt\", \"cipher\", \"fs\", \"net\" - there were no hits"
aggregated-from = "https://chromium.googlesource.com/chromium/src/+/main/third_party/rust/chromium_crates_io/supply-chain/audits.toml?format=TEXT"

[[audits.google.audits.strsim]]
who = "<EMAIL>"
criteria = "safe-to-deploy"
version = "0.10.0"
notes = """
Reviewed in https://crrev.com/c/5171063

Previously reviewed during security review and the audit is grandparented in.
"""
aggregated-from = "https://chromium.googlesource.com/chromium/src/+/main/third_party/rust/chromium_crates_io/supply-chain/audits.toml?format=TEXT"

[[audits.google.audits.version_check]]
who = "George Burgess IV <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.9.4"
aggregated-from = "https://chromium.googlesource.com/chromiumos/third_party/rust_crates/+/refs/heads/main/cargo-vet/audits.toml?format=TEXT"

[[audits.isrg.audits.block-buffer]]
who = "David Cook <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.9.0"

[[audits.isrg.audits.getrandom]]
who = "David Cook <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.12 -> 0.2.14"

[[audits.isrg.audits.getrandom]]
who = "David Cook <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.14 -> 0.2.15"

[[audits.isrg.audits.ghash]]
who = "David Cook <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.5.0"

[[audits.isrg.audits.num-traits]]
who = "David Cook <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.15 -> 0.2.16"

[[audits.isrg.audits.num-traits]]
who = "Ameer Ghani <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.16 -> 0.2.17"

[[audits.isrg.audits.num-traits]]
who = "David Cook <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.17 -> 0.2.18"

[[audits.isrg.audits.num-traits]]
who = "David Cook <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.18 -> 0.2.19"

[[audits.isrg.audits.opaque-debug]]
who = "David Cook <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.3.0"

[[audits.isrg.audits.rand_chacha]]
who = "David Cook <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.3.1"

[[audits.isrg.audits.rand_core]]
who = "David Cook <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.6.3"

[[audits.isrg.audits.universal-hash]]
who = "David Cook <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.4.1"

[[audits.isrg.audits.universal-hash]]
who = "David Cook <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.5.0 -> 0.5.1"

[[audits.mozilla.wildcard-audits.core-foundation-sys]]
who = "Bobby Holley <<EMAIL>>"
criteria = "safe-to-deploy"
user-id = 5946 # Jeff Muizelaar (jrmuizel)
start = "2020-10-14"
end = "2023-05-04"
renew = false
notes = "I've reviewed every source contribution that was neither authored nor reviewed by Mozilla."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.wildcard-audits.core-graphics]]
who = "Bobby Holley <<EMAIL>>"
criteria = "safe-to-deploy"
user-id = 5946 # Jeff Muizelaar (jrmuizel)
start = "2020-12-08"
end = "2023-05-04"
renew = false
notes = "I've reviewed every source contribution that was neither authored nor reviewed by Mozilla."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.wildcard-audits.encoding_rs]]
who = "Henri Sivonen <<EMAIL>>"
criteria = "safe-to-deploy"
user-id = 4484
start = "2019-02-26"
end = "2024-08-28"
notes = "I, Henri Sivonen, wrote encoding_rs for Gecko and have reviewed contributions by others. There are two caveats to the certification: 1) The crate does things that are documented to be UB but that do not appear to actually be UB due to integer types differing from the general rule; https://github.com/hsivonen/encoding_rs/issues/79 . 2) It would be prudent to re-review the code that reinterprets buffers of integers as SIMD vectors; see https://github.com/hsivonen/encoding_rs/issues/87 ."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.wildcard-audits.unicode-normalization]]
who = "Manish Goregaokar <<EMAIL>>"
criteria = "safe-to-deploy"
user-id = 1139 # Manish Goregaokar (Manishearth)
start = "2019-11-06"
end = "2024-05-03"
notes = "All code written or reviewed by Manish"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.wildcard-audits.unicode-segmentation]]
who = "Manish Goregaokar <<EMAIL>>"
criteria = "safe-to-deploy"
user-id = 1139 # Manish Goregaokar (Manishearth)
start = "2019-05-15"
end = "2024-05-03"
notes = "All code written or reviewed by Manish"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.android_system_properties]]
who = "Nicolas Silva <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.2"
notes = "I wrote this crate, reviewed by jimb. It is mostly a Rust port of some C++ code we already ship."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.android_system_properties]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.1.2 -> 0.1.4"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.android_system_properties]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.1.4 -> 0.1.5"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.autocfg]]
who = "Josh Stone <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.1.0"
notes = "All code written or reviewed by Josh Stone."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.bit-set]]
who = "Aria Beingessner <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.5.2"
notes = "Another crate I own via contain-rs that is ancient and maintenance mode, no known issues."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.bit-set]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.5.2 -> 0.5.3"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.bit-vec]]
who = "Aria Beingessner <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.6.3"
notes = "Another crate I own via contain-rs that is ancient and in maintenance mode but otherwise perfectly fine."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.block-buffer]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.10.2 -> 0.10.3"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.core-graphics]]
who = "Teodor Tanasoaia <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.22.3 -> 0.23.1"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.cpufeatures]]
who = "Gabriele Svelto <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.7 -> 0.2.8"
notes = "This release contains a single fix for an issue that affected Firefox"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.crypto-common]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.1.3 -> 0.1.6"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.cssparser-macros]]
who = "Emilio Cobos Álvarez <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.6.0"
notes = """
Trivial crate with a single proc macro to compute the max length of the inputs
to a match expression.
"""
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.cssparser-macros]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.6.0 -> 0.6.1"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.errno]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.1 -> 0.3.3"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.fastrand]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "1.9.0 -> 2.0.0"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.fnv]]
who = "Bobby Holley <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.0.7"
notes = "Simple hasher implementation with no unsafe code."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.foreign-types]]
who = "Teodor Tanasoaia <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.2 -> 0.5.0"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.foreign-types-macros]]
who = "Teodor Tanasoaia <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.2.3"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.foreign-types-shared]]
who = "Teodor Tanasoaia <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.1.1 -> 0.3.1"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.form_urlencoded]]
who = "Valentin Gosu <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.2.0"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.form_urlencoded]]
who = "Valentin Gosu <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "1.2.0 -> 1.2.1"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.futures-channel]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.27 -> 0.3.28"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.futures-core]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.27 -> 0.3.28"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.fxhash]]
who = "Bobby Holley <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.2.1"
notes = "Straightforward crate with no unsafe code, does what it says on the tin."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.hashbrown]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.12.3"
notes = "This version is used in rust's libstd, so effectively we're already trusting it"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.heck]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.4.0 -> 0.4.1"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.hex]]
who = "Simon Friedberger <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.4.3"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.idna]]
who = "Valentin Gosu <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.4.0 -> 0.5.0"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.lazy_static]]
who = "Nika Layzell <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.4.0"
notes = "I have read over the macros, and audited the unsafe code."
aggregated-from = "https://raw.githubusercontent.com/mozilla/cargo-vet/main/supply-chain/audits.toml"

[[audits.mozilla.audits.libc]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.147 -> 0.2.148"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.libloading]]
who = "Erich Gubler <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.7.4 -> 0.8.3"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.linked-hash-map]]
who = "Aria Beingessner <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.5.4"
notes = "I own this crate (I am contain-rs) and 0.5.4 passes miri. This code is very old and used by lots of people, so I'm pretty confident in it, even though it's in maintenance-mode and missing some nice-to-have APIs."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.linked-hash-map]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-run"
delta = "0.5.4 -> 0.5.6"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.log]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.4.17"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.log]]
who = "Jan-Erik Rediger <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.4.17 -> 0.4.18"
notes = "One dependency removed, others updated (which we don't rely on), some APIs (which we don't use) changed."
aggregated-from = "https://raw.githubusercontent.com/mozilla/glean/main/supply-chain/audits.toml"

[[audits.mozilla.audits.log]]
who = "Kagami Sascha Rosylight <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.4.18 -> 0.4.20"
notes = "Only cfg attribute and internal macro changes and module refactorings"
aggregated-from = "https://raw.githubusercontent.com/mozilla/glean/main/supply-chain/audits.toml"

[[audits.mozilla.audits.malloc_buf]]
who = "Bobby Holley <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.0.6"
notes = """
Very small crate for managing malloc-ed buffers, primarily for use in the objc crate.
There is an edge-case condition that passes slice::from_raw_parts(0x1, 0) which I'm
not entirely certain is technically sound, but in either case I am reasonably confident
it's not exploitable.
"""
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.matches]]
who = "Bobby Holley <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.9"
notes = "This is a trivial crate."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.matches]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.1.9 -> 0.1.10"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.new_debug_unreachable]]
who = "Bobby Holley <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.0.4"
notes = "This is a trivial crate."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.num-traits]]
who = "Josh Stone <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.2.15"
notes = "All code written or reviewed by Josh Stone."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.percent-encoding]]
who = "Valentin Gosu <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "2.2.0 -> 2.3.0"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.percent-encoding]]
who = "Valentin Gosu <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "2.3.0 -> 2.3.1"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.precomputed-hash]]
who = "Bobby Holley <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.1"
notes = "This is a trivial crate."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.quote]]
who = "Nika Layzell <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.0.18"
notes = """
`quote` is a utility crate used by proc-macros to generate TokenStreams
conveniently from source code. The bulk of the logic is some complex
interlocking `macro_rules!` macros which are used to parse and build the
`TokenStream` within the proc-macro.

This crate contains no unsafe code, and the internal logic, while difficult to
read, is generally straightforward. I have audited the the quote macros, ident
formatter, and runtime logic.
"""
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.quote]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "1.0.18 -> 1.0.21"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.quote]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "1.0.21 -> 1.0.23"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.quote]]
who = "Jan-Erik Rediger <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "1.0.27 -> 1.0.28"
notes = "Enabled on wasm targets"
aggregated-from = "https://raw.githubusercontent.com/mozilla/glean/main/supply-chain/audits.toml"

[[audits.mozilla.audits.quote]]
who = "Jan-Erik Rediger <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "1.0.28 -> 1.0.31"
notes = "Minimal changes and removal of the build.rs"
aggregated-from = "https://raw.githubusercontent.com/mozilla/glean/main/supply-chain/audits.toml"

[[audits.mozilla.audits.rand_core]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.6.3 -> 0.6.4"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.raw-window-handle]]
who = "Jim Blandy <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.5.0"
notes = "I looked through all the sources of the v0.5.0 crate."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.raw-window-handle]]
who = "Mike Hommey <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.5.0 -> 0.5.2"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.raw-window-handle]]
who = "Nicolas Silva <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.5.2 -> 0.6.0"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.rustversion]]
who = "Bobby Holley <<EMAIL>>"
criteria = "safe-to-deploy"
version = "1.0.9"
notes = """
This crate has a build-time component and procedural macro logic, which I looked
at enough to convince myself it wasn't going to do anything dramatically wrong.
I don't think logic bugs in the version parsing etc can realistically introduce
a security vulnerability.
"""
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.rustversion]]
who = "Jan-Erik Rediger <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "1.0.9 -> 1.0.14"
notes = "Doc updates, minimal CI changes and a fix to build-script reruns"
aggregated-from = "https://raw.githubusercontent.com/mozilla/glean/main/supply-chain/audits.toml"

[[audits.mozilla.audits.selectors]]
who = "Emilio Cobos Álvarez <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.22.0"
notes = """
This crate is basically developed in-tree. Mozilla employees have either
reviewed or written virtually all of the code.
"""
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.servo_arc]]
who = "Emilio Cobos Álvarez <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.1"
notes = "Developed in-tree, effectively."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.subtle]]
who = "Simon Friedberger <<EMAIL>>"
criteria = "safe-to-deploy"
version = "2.5.0"
notes = "The goal is to provide some constant-time correctness for cryptographic implementations. The approach is reasonable, it is known to be insufficient but this is pointed out in the documentation."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.time-core]]
who = "Kershaw Chang <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.0"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.time-core]]
who = "Kershaw Chang <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.1.0 -> 0.1.1"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.unicode-bidi]]
who = "Makoto Kato <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.8 -> 0.3.13"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.unicode-bidi]]
who = "Jonathan Kew <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.13 -> 0.3.14"
notes = "I am the author of the bulk of the upstream changes in this version, and also checked the remaining post-0.3.13 changes."
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.unicode-bidi]]
who = "Jonathan Kew <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.14 -> 0.3.15"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.unicode-ident]]
who = "Jan-Erik Rediger <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "1.0.8 -> 1.0.9"
notes = "Dependency updates only"
aggregated-from = "https://raw.githubusercontent.com/mozilla/glean/main/supply-chain/audits.toml"

[[audits.mozilla.audits.url]]
who = "Valentin Gosu <<EMAIL>>"
criteria = "safe-to-deploy"
version = "2.4.0"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.url]]
who = "Valentin Gosu <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "2.4.0 -> 2.4.1"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.mozilla.audits.url]]
who = "Valentin Gosu <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "2.4.1 -> 2.5.0"
aggregated-from = "https://hg.mozilla.org/mozilla-central/raw-file/tip/supply-chain/audits.toml"

[[audits.zcash.audits.block-buffer]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.10.3 -> 0.10.4"
notes = "Adds panics to prevent a block size of zero from causing unsoundness."
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.bumpalo]]
who = "Daira-Emma Hopwood <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "3.15.4 -> 3.16.0"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.cipher]]
who = "Daira Hopwood <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.0 -> 0.4.3"
notes = "Significant rework of (mainly RustCrypto-internal) APIs."
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.cipher]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.4.3 -> 0.4.4"
notes = "Adds panics to prevent a block size of zero from causing unsoundness."
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.cpufeatures]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.8 -> 0.2.9"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.cpufeatures]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.9 -> 0.2.11"
notes = """
New `unsafe` block is to call `libc::getauxval(libc::AT_HWCAP)` on Linux for
LoongArch64 CPU feature detection support. This and the supporting macro code is
the same as the existing Linux code for AArch64.
"""
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.cpufeatures]]
who = "Daira-Emma Hopwood <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.11 -> 0.2.12"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.errno]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.3 -> 0.3.8"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.futures-channel]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.28 -> 0.3.29"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.futures-channel]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.29 -> 0.3.30"
notes = "Removes `build.rs` now that it can rely on the `target_has_atomic` attribute."
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.futures-core]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.28 -> 0.3.29"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.futures-core]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.29 -> 0.3.30"
notes = "Removes `build.rs` now that it can rely on the `target_has_atomic` attribute."
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.inout]]
who = "Daira Hopwood <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.1.3"
notes = "Reviewed in full."
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.libredox]]
who = "Daira-Emma Hopwood <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.0.1 -> 0.1.3"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.log]]
who = "Daira-Emma Hopwood <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.4.20 -> 0.4.21"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.opaque-debug]]
who = "Daira-Emma Hopwood <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.0 -> 0.3.1"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.pin-project-lite]]
who = "Daira-Emma Hopwood <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.13 -> 0.2.14"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.quote]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "1.0.31 -> 1.0.33"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.quote]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "1.0.33 -> 1.0.35"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.rand_xorshift]]
who = "Sean Bowe <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.3.0"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.redox_users]]
who = "Daira-Emma Hopwood <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.4.4 -> 0.4.5"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.rustc-demangle]]
who = "Sean Bowe <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.1.21 -> 0.1.22"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.rustc-demangle]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.1.22 -> 0.1.23"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.rustc_version]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.4.0"
notes = """
Most of the crate is code to parse and validate the output of `rustc -vV`. The caller can
choose which `rustc` to use, or can use `rustc_version::{version, version_meta}` which will
try `$RUSTC` followed by `rustc`.

If an adversary can arbitrarily set the `$RUSTC` environment variable then this crate will
execute arbitrary code. But when this crate is used within a build script, `$RUSTC` should
be set correctly by `cargo`.
"""
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.sharded-slab]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.1.4 -> 0.1.7"
notes = "Only change to an `unsafe` block is to fix a clippy lint."
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.time-core]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.1.1 -> 0.1.2"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.tinyvec_macros]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.1.0 -> 0.1.1"
notes = "Adds `#![forbid(unsafe_code)]` and license files."
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.toml_datetime]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.5.1"
notes = "Crate has `#![forbid(unsafe_code)]`, no `unwrap / expect / panic`, no ambient capabilities."
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.toml_datetime]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.5.1 -> 0.6.1"
notes = "Fixes a bug in parsing negative minutes in datetime string offsets."
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.toml_datetime]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.6.2 -> 0.6.3"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.tracing-subscriber]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.17 -> 0.3.18"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.try-lock]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.2.4 -> 0.2.5"
notes = "Bumps MSRV to remove unsafe code block."
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.unicode-ident]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "1.0.9 -> 1.0.12"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.universal-hash]]
who = "Daira Hopwood <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.4.1 -> 0.5.0"
notes = "I checked correctness of to_blocks which uses unsafe code in a safe function."
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.want]]
who = "Jack Grigg <<EMAIL>>"
criteria = "safe-to-deploy"
delta = "0.3.0 -> 0.3.1"
notes = """
Migrates to `try-lock 0.2.4` to replace some unsafe APIs that were not marked
`unsafe` (but that were being used safely).
"""
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"

[[audits.zcash.audits.wasm-bindgen-macro-support]]
who = "Daira-Emma Hopwood <<EMAIL>>"
criteria = "safe-to-deploy"
version = "0.2.92"
aggregated-from = "https://raw.githubusercontent.com/zcash/zcash/master/qa/supply-chain/audits.toml"
