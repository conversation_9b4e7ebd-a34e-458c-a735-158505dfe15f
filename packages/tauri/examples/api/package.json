{"name": "api", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "node ../../packages/cli/tauri.js"}, "dependencies": {"@tauri-apps/api": "../../packages/api/dist"}, "devDependencies": {"@iconify-json/codicon": "^1.2.24", "@iconify-json/ph": "^1.2.2", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@unocss/extractor-svelte": "^66.3.3", "svelte": "^5.35.6", "unocss": "^66.3.3", "vite": "^7.0.4"}}