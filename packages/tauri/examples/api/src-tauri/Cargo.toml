[package]
name = "api"
version = "0.1.0"
description = "An example Tauri Application showcasing the api"
edition = "2021"
rust-version = "1.77.2"
license = "Apache-2.0 OR MIT"

[lib]
name = "api_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { path = "../../../crates/tauri-build", features = [
  "codegen",
  "isolation",
] }

[dependencies]
serde_json = "1"
serde = { version = "1", features = ["derive"] }
tiny_http = "0.11"
log = "0.4.21"
tauri-plugin-sample = { path = "./tauri-plugin-sample/" }
tauri-plugin-log = "2"

[dependencies.tauri]
path = "../../../crates/tauri"
features = [
  "protocol-asset",
  "image-ico",
  "image-png",
  "isolation",
  "macos-private-api",
  "tray-icon",
]

[dev-dependencies.tauri]
path = "../../../crates/tauri"
features = ["test"]

[features]
prod = ["tauri/custom-protocol"]
