<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Welcome to Tauri!</title>
  </head>
  <body>
    <h1>Welcome to Tauri!</h1>

    <form id="form">
      <input id="name" placeholder="Enter a name..." />
      <button>Greet</button>
    </form>

    <p id="message"></p>

    <script>
      const { invoke } = window.__TAURI__.core

      const form = document.querySelector('#form')
      const nameEl = document.querySelector('#name')
      const messageEl = document.querySelector('#message')

      form.addEventListener('submit', async (e) => {
        e.preventDefault()

        const name = nameEl.value
        const newMessage = await invoke('greet', { name })
        messageEl.textContent = newMessage
      })
    </script>
  </body>
</html>
