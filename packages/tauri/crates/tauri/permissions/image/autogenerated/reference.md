## Default Permission

Default permissions for the plugin, which enables all commands.

#### This default permission set includes the following:

- `allow-new`
- `allow-from-bytes`
- `allow-from-path`
- `allow-rgba`
- `allow-size`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`core:image:allow-from-bytes`

</td>
<td>

Enables the from_bytes command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:image:deny-from-bytes`

</td>
<td>

Denies the from_bytes command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:image:allow-from-path`

</td>
<td>

Enables the from_path command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:image:deny-from-path`

</td>
<td>

Denies the from_path command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:image:allow-new`

</td>
<td>

Enables the new command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:image:deny-new`

</td>
<td>

Denies the new command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:image:allow-rgba`

</td>
<td>

Enables the rgba command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:image:deny-rgba`

</td>
<td>

Denies the rgba command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:image:allow-size`

</td>
<td>

Enables the size command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:image:deny-size`

</td>
<td>

Denies the size command without any pre-configured scope.

</td>
</tr>
</table>
