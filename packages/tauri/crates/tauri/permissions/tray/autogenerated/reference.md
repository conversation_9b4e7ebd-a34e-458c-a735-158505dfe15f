## Default Permission

Default permissions for the plugin, which enables all commands.

#### This default permission set includes the following:

- `allow-new`
- `allow-get-by-id`
- `allow-remove-by-id`
- `allow-set-icon`
- `allow-set-menu`
- `allow-set-tooltip`
- `allow-set-title`
- `allow-set-visible`
- `allow-set-temp-dir-path`
- `allow-set-icon-as-template`
- `allow-set-show-menu-on-left-click`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`core:tray:allow-get-by-id`

</td>
<td>

Enables the get_by_id command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:deny-get-by-id`

</td>
<td>

Denies the get_by_id command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:allow-new`

</td>
<td>

Enables the new command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:deny-new`

</td>
<td>

Denies the new command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:allow-remove-by-id`

</td>
<td>

Enables the remove_by_id command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:deny-remove-by-id`

</td>
<td>

Denies the remove_by_id command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:allow-set-icon`

</td>
<td>

Enables the set_icon command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:deny-set-icon`

</td>
<td>

Denies the set_icon command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:allow-set-icon-as-template`

</td>
<td>

Enables the set_icon_as_template command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:deny-set-icon-as-template`

</td>
<td>

Denies the set_icon_as_template command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:allow-set-menu`

</td>
<td>

Enables the set_menu command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:deny-set-menu`

</td>
<td>

Denies the set_menu command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:allow-set-show-menu-on-left-click`

</td>
<td>

Enables the set_show_menu_on_left_click command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:deny-set-show-menu-on-left-click`

</td>
<td>

Denies the set_show_menu_on_left_click command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:allow-set-temp-dir-path`

</td>
<td>

Enables the set_temp_dir_path command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:deny-set-temp-dir-path`

</td>
<td>

Denies the set_temp_dir_path command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:allow-set-title`

</td>
<td>

Enables the set_title command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:deny-set-title`

</td>
<td>

Denies the set_title command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:allow-set-tooltip`

</td>
<td>

Enables the set_tooltip command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:deny-set-tooltip`

</td>
<td>

Denies the set_tooltip command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:allow-set-visible`

</td>
<td>

Enables the set_visible command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`core:tray:deny-set-visible`

</td>
<td>

Denies the set_visible command without any pre-configured scope.

</td>
</tr>
</table>
